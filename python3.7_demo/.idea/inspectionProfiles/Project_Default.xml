<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="50">
            <item index="0" class="java.lang.String" itemvalue="numba" />
            <item index="1" class="java.lang.String" itemvalue="pytest-rerunfailures" />
            <item index="2" class="java.lang.String" itemvalue="greenlet" />
            <item index="3" class="java.lang.String" itemvalue="bytedevtest" />
            <item index="4" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="5" class="java.lang.String" itemvalue="deepdiff" />
            <item index="6" class="java.lang.String" itemvalue="bytedpymongo" />
            <item index="7" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="8" class="java.lang.String" itemvalue="mido" />
            <item index="9" class="java.lang.String" itemvalue="certifi" />
            <item index="10" class="java.lang.String" itemvalue="music21" />
            <item index="11" class="java.lang.String" itemvalue="pyparsing" />
            <item index="12" class="java.lang.String" itemvalue="iniconfig" />
            <item index="13" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="14" class="java.lang.String" itemvalue="attrs" />
            <item index="15" class="java.lang.String" itemvalue="bytedeuler" />
            <item index="16" class="java.lang.String" itemvalue="execnet" />
            <item index="17" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="18" class="java.lang.String" itemvalue="librosa" />
            <item index="19" class="java.lang.String" itemvalue="allure-python-commons" />
            <item index="20" class="java.lang.String" itemvalue="pytest-html" />
            <item index="21" class="java.lang.String" itemvalue="idna" />
            <item index="22" class="java.lang.String" itemvalue="pluggy" />
            <item index="23" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="24" class="java.lang.String" itemvalue="bytedtos" />
            <item index="25" class="java.lang.String" itemvalue="Levenshtein" />
            <item index="26" class="java.lang.String" itemvalue="pytest-xdist" />
            <item index="27" class="java.lang.String" itemvalue="requests" />
            <item index="28" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="29" class="java.lang.String" itemvalue="pytest-forked" />
            <item index="30" class="java.lang.String" itemvalue="zipp" />
            <item index="31" class="java.lang.String" itemvalue="jsonpath" />
            <item index="32" class="java.lang.String" itemvalue="xmltodict" />
            <item index="33" class="java.lang.String" itemvalue="jsonpath_ng" />
            <item index="34" class="java.lang.String" itemvalue="pytest-timeout" />
            <item index="35" class="java.lang.String" itemvalue="urllib3" />
            <item index="36" class="java.lang.String" itemvalue="six" />
            <item index="37" class="java.lang.String" itemvalue="pytest" />
            <item index="38" class="java.lang.String" itemvalue="aiofiles" />
            <item index="39" class="java.lang.String" itemvalue="packaging" />
            <item index="40" class="java.lang.String" itemvalue="allure-pytest" />
            <item index="41" class="java.lang.String" itemvalue="python-gitlab" />
            <item index="42" class="java.lang.String" itemvalue="chardet" />
            <item index="43" class="java.lang.String" itemvalue="pandas" />
            <item index="44" class="java.lang.String" itemvalue="tqdm" />
            <item index="45" class="java.lang.String" itemvalue="soundfile" />
            <item index="46" class="java.lang.String" itemvalue="bytedmysql" />
            <item index="47" class="java.lang.String" itemvalue="aiohttp" />
            <item index="48" class="java.lang.String" itemvalue="openpyxl" />
            <item index="49" class="java.lang.String" itemvalue="requests-toolbelt" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>