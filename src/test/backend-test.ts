/**
 * 豆包实时语音API后端测试
 * 测试后端代理服务的各项功能
 */

import { DoubaoRealtimeClient, ConnectionState } from '@/lib/doubao-client';

// 测试配置
const TEST_CONFIG = {
  clientId: `test-${Date.now()}`,
  timeout: 30000 // 30秒超时
};

/**
 * 测试后端连接
 */
async function testBackendConnection() {
  console.log('🔗 测试后端连接...');
  
  try {
    // 测试后端状态
    const response = await fetch('/api/realtime/ws');
    const status = await response.json();
    
    console.log('✅ 后端状态:', status);
    
    if (!status.config?.proxyEnabled) {
      throw new Error('代理服务未启用');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 后端连接失败:', error);
    return false;
  }
}

/**
 * 测试豆包API连接
 */
async function testDoubaoConnection() {
  console.log('🔗 测试豆包API连接...');
  
  try {
    const response = await fetch('/api/realtime/ws', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'connect',
        clientId: TEST_CONFIG.clientId
      })
    });
    
    const result = await response.json();
    console.log('✅ 豆包API连接结果:', result);
    
    if (!result.success) {
      throw new Error(result.error || '连接失败');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 豆包API连接失败:', error);
    return false;
  }
}

/**
 * 测试消息监听
 */
async function testMessageListener() {
  console.log('👂 测试消息监听...');
  
  return new Promise<boolean>((resolve) => {
    const timeout = setTimeout(() => {
      console.log('⏰ 消息监听测试超时');
      resolve(false);
    }, 10000);
    
    fetch('/api/realtime/ws', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'listen',
        clientId: TEST_CONFIG.clientId
      })
    }).then(response => {
      if (!response.ok) {
        throw new Error('监听请求失败');
      }
      
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }
      
      const decoder = new TextDecoder();
      let messageCount = 0;
      
      const readMessages = async () => {
        try {
          const { done, value } = await reader.read();
          if (done) {
            clearTimeout(timeout);
            resolve(messageCount > 0);
            return;
          }
          
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                console.log('📨 收到消息:', data.type);
                messageCount++;
                
                if (data.type === 'connection_ready') {
                  console.log('✅ 消息监听器就绪');
                  clearTimeout(timeout);
                  resolve(true);
                  return;
                }
              } catch (error) {
                console.error('解析消息失败:', error);
              }
            }
          }
          
          // 继续读取
          readMessages();
        } catch (error) {
          console.error('❌ 读取消息失败:', error);
          clearTimeout(timeout);
          resolve(false);
        }
      };
      
      readMessages();
    }).catch(error => {
      console.error('❌ 启动消息监听失败:', error);
      clearTimeout(timeout);
      resolve(false);
    });
  });
}

/**
 * 测试客户端SDK
 */
async function testClientSDK() {
  console.log('🔧 测试客户端SDK...');
  
  try {
    const client = new DoubaoRealtimeClient();
    
    // 监听事件
    let eventReceived = false;
    client.on('connectionStateChange', (state: ConnectionState) => {
      console.log('📡 连接状态变化:', state);
      eventReceived = true;
    });
    
    // 连接
    await client.connect();
    console.log('✅ 客户端连接成功');
    
    // 检查事件
    if (!eventReceived) {
      console.log('⚠️ 未收到连接状态变化事件');
    }
    
    // 断开连接
    await client.disconnect();
    console.log('✅ 客户端断开成功');
    
    return true;
  } catch (error) {
    console.error('❌ 客户端SDK测试失败:', error);
    return false;
  }
}

/**
 * 清理测试连接
 */
async function cleanup() {
  console.log('🧹 清理测试连接...');
  
  try {
    await fetch('/api/realtime/ws', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'disconnect',
        clientId: TEST_CONFIG.clientId
      })
    });
    
    console.log('✅ 清理完成');
  } catch (error) {
    console.error('❌ 清理失败:', error);
  }
}

/**
 * 运行所有测试
 */
export async function runBackendTests() {
  console.log('🚀 开始后端测试...\n');
  
  const results = {
    backendConnection: false,
    doubaoConnection: false,
    messageListener: false,
    clientSDK: false
  };
  
  try {
    // 测试后端连接
    results.backendConnection = await testBackendConnection();
    console.log('');
    
    if (results.backendConnection) {
      // 测试豆包API连接
      results.doubaoConnection = await testDoubaoConnection();
      console.log('');
      
      if (results.doubaoConnection) {
        // 测试消息监听
        results.messageListener = await testMessageListener();
        console.log('');
      }
    }
    
    // 测试客户端SDK
    results.clientSDK = await testClientSDK();
    console.log('');
    
  } finally {
    // 清理
    await cleanup();
  }
  
  // 输出测试结果
  console.log('📊 测试结果:');
  console.log('- 后端连接:', results.backendConnection ? '✅' : '❌');
  console.log('- 豆包API连接:', results.doubaoConnection ? '✅' : '❌');
  console.log('- 消息监听:', results.messageListener ? '✅' : '❌');
  console.log('- 客户端SDK:', results.clientSDK ? '✅' : '❌');
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 测试通过: ${passedTests}/${totalTests}`);
  
  return results;
}

// 如果直接运行此文件
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).runBackendTests = runBackendTests;
  console.log('💡 在浏览器控制台中运行: runBackendTests()');
}
