/**
 * 豆包实时语音API后端测试 - WebSocket直连版本
 * 测试WebSocket服务器的各项功能
 */

import { DoubaoRealtimeClient, ConnectionState } from '@/lib/doubao-client';

// 测试配置
const TEST_CONFIG = {
  clientId: `test-${Date.now()}`,
  timeout: 30000 // 30秒超时
};

/**
 * 测试WebSocket服务器连接
 */
async function testWebSocketServer() {
  console.log('🔗 测试WebSocket服务器...');

  return new Promise<boolean>((resolve) => {
    try {
      const ws = new WebSocket('ws://localhost:3000/api/realtime/ws');

      const timeout = setTimeout(() => {
        ws.close();
        console.log('❌ WebSocket连接超时');
        resolve(false);
      }, 5000);

      ws.onopen = () => {
        clearTimeout(timeout);
        console.log('✅ WebSocket服务器连接成功');
        ws.close();
        resolve(true);
      };

      ws.onerror = (error) => {
        clearTimeout(timeout);
        console.error('❌ WebSocket连接失败:', error);
        resolve(false);
      };

    } catch (error) {
      console.error('❌ WebSocket测试失败:', error);
      resolve(false);
    }
  });
}

/**
 * 测试豆包API连接（通过WebSocket）
 */
async function testDoubaoConnection() {
  console.log('🔗 测试豆包API连接...');

  return new Promise<boolean>((resolve) => {
    try {
      const ws = new WebSocket('ws://localhost:3000/api/realtime/ws');

      const timeout = setTimeout(() => {
        ws.close();
        console.log('❌ 豆包API连接超时');
        resolve(false);
      }, 10000);

      let connectionReceived = false;

      ws.onopen = () => {
        console.log('✅ WebSocket连接已建立，等待豆包API连接确认...');
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'connection' && data.status === 'connected') {
            clearTimeout(timeout);
            connectionReceived = true;
            console.log('✅ 豆包API连接成功:', data);
            ws.close();
            resolve(true);
          }
        } catch (error) {
          // 可能是二进制数据，忽略
        }
      };

      ws.onerror = (error) => {
        clearTimeout(timeout);
        console.error('❌ 豆包API连接失败:', error);
        resolve(false);
      };

      ws.onclose = () => {
        clearTimeout(timeout);
        if (!connectionReceived) {
          console.log('❌ WebSocket连接关闭，未收到豆包API连接确认');
          resolve(false);
        }
      };

    } catch (error) {
      console.error('❌ 豆包API连接测试失败:', error);
      resolve(false);
    }
  });
}

/**
 * 测试消息传输
 */
async function testMessageTransmission() {
  console.log('📨 测试消息传输...');

  return new Promise<boolean>((resolve) => {
    try {
      const ws = new WebSocket('ws://localhost:3000/api/realtime/ws');

      const timeout = setTimeout(() => {
        ws.close();
        console.log('⏰ 消息传输测试超时');
        resolve(false);
      }, 10000);

      let messageReceived = false;

      ws.onopen = () => {
        console.log('✅ WebSocket连接已建立，发送测试消息...');

        // 发送一个简单的测试消息
        const testMessage = JSON.stringify({
          type: 'test',
          data: 'hello'
        });
        ws.send(testMessage);
      };

      ws.onmessage = (event) => {
        console.log('📨 收到消息:', event.data.length, '字节');
        messageReceived = true;

        // 检查是否是连接确认消息
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'connection') {
            console.log('✅ 收到连接确认消息');
            clearTimeout(timeout);
            ws.close();
            resolve(true);
          }
        } catch (error) {
          // 可能是二进制数据
          console.log('✅ 收到二进制消息');
          clearTimeout(timeout);
          ws.close();
          resolve(true);
        }
      };

      ws.onerror = (error) => {
        clearTimeout(timeout);
        console.error('❌ 消息传输测试失败:', error);
        resolve(false);
      };

      ws.onclose = () => {
        clearTimeout(timeout);
        if (!messageReceived) {
          console.log('❌ 未收到任何消息');
          resolve(false);
        }
      };

    } catch (error) {
      console.error('❌ 消息传输测试失败:', error);
      resolve(false);
    }
  });
}

/**
 * 测试客户端SDK
 */
async function testClientSDK() {
  console.log('🔧 测试客户端SDK...');
  
  try {
    const client = new DoubaoRealtimeClient();
    
    // 监听事件
    let eventReceived = false;
    client.on('connectionStateChange', (state: ConnectionState) => {
      console.log('📡 连接状态变化:', state);
      eventReceived = true;
    });
    
    // 连接
    await client.connect();
    console.log('✅ 客户端连接成功');
    
    // 检查事件
    if (!eventReceived) {
      console.log('⚠️ 未收到连接状态变化事件');
    }
    
    // 断开连接
    await client.disconnect();
    console.log('✅ 客户端断开成功');
    
    return true;
  } catch (error) {
    console.error('❌ 客户端SDK测试失败:', error);
    return false;
  }
}

/**
 * 清理测试连接
 */
async function cleanup() {
  console.log('🧹 清理测试连接...');

  try {
    // WebSocket连接会自动清理，无需手动操作
    console.log('✅ 清理完成');
  } catch (error) {
    console.error('❌ 清理失败:', error);
  }
}

/**
 * 运行所有测试
 */
export async function runBackendTests() {
  console.log('🚀 开始WebSocket后端测试...\n');

  const results = {
    webSocketServer: false,
    doubaoConnection: false,
    messageTransmission: false,
    clientSDK: false
  };

  try {
    // 测试WebSocket服务器
    results.webSocketServer = await testWebSocketServer();
    console.log('');

    if (results.webSocketServer) {
      // 测试豆包API连接
      results.doubaoConnection = await testDoubaoConnection();
      console.log('');

      if (results.doubaoConnection) {
        // 测试消息传输
        results.messageTransmission = await testMessageTransmission();
        console.log('');
      }
    }

    // 测试客户端SDK
    results.clientSDK = await testClientSDK();
    console.log('');

  } finally {
    // 清理
    await cleanup();
  }

  // 输出测试结果
  console.log('📊 测试结果:');
  console.log('- WebSocket服务器:', results.webSocketServer ? '✅' : '❌');
  console.log('- 豆包API连接:', results.doubaoConnection ? '✅' : '❌');
  console.log('- 消息传输:', results.messageTransmission ? '✅' : '❌');
  console.log('- 客户端SDK:', results.clientSDK ? '✅' : '❌');

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\n🎯 测试通过: ${passedTests}/${totalTests}`);

  return results;
}

// 如果直接运行此文件
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).runBackendTests = runBackendTests;
  console.log('💡 在浏览器控制台中运行: runBackendTests()');
}
