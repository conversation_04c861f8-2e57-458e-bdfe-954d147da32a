/**
 * WebSocket代理API路由 - Next.js后端实现
 * 在Next.js API中创建WebSocket连接到豆包API，并提供代理服务
 */

import { NextRequest } from 'next/server';
import WebSocket from 'ws';

// 豆包API配置
const DOUBAO_CONFIG = {
  appId: process.env.DOUBAO_APP_ID || '2919022726',
  accessToken: process.env.DOUBAO_ACCESS_TOKEN || 'EDQbT-FiT72TPSeOMnePDef1OFKYCAp5',
  resourceId: process.env.DOUBAO_RESOURCE_ID || 'volc.speech.dialog'
};

// 存储活跃的连接
const activeConnections = new Map<string, WebSocket>();

/**
 * 创建到豆包API的WebSocket连接
 */
async function createDoubaoConnection(clientId: string): Promise<WebSocket> {
  console.log(`为客户端 ${clientId} 创建豆包API连接`);

  const doubaoWs = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue', {
    headers: {
      'X-Api-App-ID': DOUBAO_CONFIG.appId,
      'X-Api-Access-Key': DOUBAO_CONFIG.accessToken,
      'X-Api-Resource-Id': DOUBAO_CONFIG.resourceId,
      'User-Agent': 'DoubaoRealtimeClient/1.0'
    }
  });

  return new Promise((resolve, reject) => {
    doubaoWs.on('open', () => {
      console.log(`豆包API连接已建立 - 客户端: ${clientId}`);
      activeConnections.set(clientId, doubaoWs);
      resolve(doubaoWs);
    });

    doubaoWs.on('error', (error) => {
      console.error(`豆包API连接错误 - 客户端: ${clientId}`, error);
      reject(error);
    });

    doubaoWs.on('close', (code, reason) => {
      console.log(`豆包API连接关闭 - 客户端: ${clientId}`, code, reason);
      activeConnections.delete(clientId);
    });
  });
}

/**
 * POST - WebSocket代理操作
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, clientId } = body;

    if (action === 'connect') {
      // 创建到豆包API的连接
      const doubaoWs = await createDoubaoConnection(clientId);

      return new Response(JSON.stringify({
        success: true,
        message: '豆包API连接已建立',
        clientId,
        status: 'connected'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (action === 'send') {
      // 转发消息到豆包API
      const { data } = body;
      const doubaoWs = activeConnections.get(clientId);

      if (!doubaoWs || doubaoWs.readyState !== WebSocket.OPEN) {
        return new Response(JSON.stringify({
          success: false,
          error: '豆包API连接不可用'
        }), { status: 400 });
      }

      // 发送数据到豆包API
      if (typeof data === 'string') {
        doubaoWs.send(data);
      } else {
        const buffer = Buffer.from(data, 'base64');
        doubaoWs.send(buffer);
      }

      return new Response(JSON.stringify({
        success: true,
        message: '消息已发送到豆包API'
      }));
    }

    if (action === 'disconnect') {
      // 断开豆包API连接
      const doubaoWs = activeConnections.get(clientId);
      if (doubaoWs) {
        doubaoWs.close();
        activeConnections.delete(clientId);
      }

      return new Response(JSON.stringify({
        success: true,
        message: '连接已断开'
      }));
    }

    return new Response(JSON.stringify({
      success: false,
      error: '未知操作'
    }), { status: 400 });

  } catch (error) {
    console.error('WebSocket代理错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), { status: 500 });
  }
}

/**
 * GET - 获取连接状态
 */
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const clientId = url.searchParams.get('clientId');

  if (clientId) {
    const doubaoWs = activeConnections.get(clientId);
    const isConnected = doubaoWs && doubaoWs.readyState === WebSocket.OPEN;

    return new Response(JSON.stringify({
      clientId,
      connected: isConnected,
      activeConnections: activeConnections.size
    }));
  }

  return new Response(JSON.stringify({
    message: 'WebSocket代理服务',
    activeConnections: activeConnections.size,
    config: {
      doubaoApiUrl: 'wss://openspeech.bytedance.com/api/v3/realtime/dialogue',
      proxyEnabled: true
    }
  }));
}