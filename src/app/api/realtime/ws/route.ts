/**
 * WebSocket代理API路由 - Next.js后端实现
 * 在Next.js API中创建WebSocket连接到豆包API，并提供代理服务
 * 基于豆包实时语音大模型API文档实现
 */

import { NextRequest } from 'next/server';
import WebSocket from 'ws';
import { decodeHeader, MessageType, ServerEventId } from '@/lib/protocol';

// 豆包API配置
const DOUBAO_CONFIG = {
  appId: process.env.DOUBAO_APP_ID || '2919022726',
  accessToken: process.env.DOUBAO_ACCESS_TOKEN || 'EDQbT-FiT72TPSeOMnePDef1OFKYCAp5',
  resourceId: process.env.DOUBAO_RESOURCE_ID || 'volc.speech.dialog',
  appKey: 'PlgvMymc7f3tQnJ6' // 文档要求的固定值
};

// 连接信息接口
interface ConnectionInfo {
  ws: WebSocket;
  clientCallbacks: Set<(data: any) => void>;
  lastActivity: number;
}

// 存储活跃的连接
const activeConnections = new Map<string, ConnectionInfo>();

// 清理超时连接的定时器
setInterval(() => {
  const now = Date.now();
  const timeout = 10 * 60 * 1000; // 10分钟超时

  for (const [clientId, info] of activeConnections.entries()) {
    if (now - info.lastActivity > timeout) {
      console.log(`清理超时连接: ${clientId}`);
      info.ws.close();
      activeConnections.delete(clientId);
    }
  }
}, 60 * 1000); // 每分钟检查一次

/**
 * 解析豆包API返回的二进制协议消息
 */
function parseDoubaoMessage(data: Buffer): any {
  try {
    // 解析协议头
    const header = decodeHeader(data);
    console.log('收到豆包API消息:', {
      messageType: header.messageType,
      serializationMethod: header.serializationMethod,
      compressionMethod: header.compressionMethod
    });

    // 根据消息类型处理
    if (header.messageType === MessageType.FULL_SERVER_RESPONSE) {
      // 文本事件响应
      return parseServerResponse(data, header);
    } else if (header.messageType === MessageType.AUDIO_ONLY_RESPONSE) {
      // 音频数据响应
      return parseAudioResponse(data, header);
    } else if (header.messageType === MessageType.ERROR_INFORMATION) {
      // 错误信息
      return parseErrorResponse(data, header);
    }

    return null;
  } catch (error) {
    console.error('解析豆包API消息失败:', error);
    return null;
  }
}

/**
 * 解析服务器文本响应
 */
function parseServerResponse(data: Buffer, header: any): any {
  try {
    // 跳过协议头(4字节)
    let offset = 4;

    // 解析可选字段
    const eventId = data.readUInt32LE(offset);
    offset += 4;

    // 解析session ID长度和内容
    const sessionIdSize = data.readUInt32LE(offset);
    offset += 4;
    const sessionId = data.subarray(offset, offset + sessionIdSize).toString('utf8');
    offset += sessionIdSize;

    // 解析payload长度
    const payloadSize = data.readUInt32LE(offset);
    offset += 4;

    // 解析payload
    let payload = data.subarray(offset, offset + payloadSize);

    // 如果使用了压缩，需要解压
    if (header.compressionMethod === 1) { // GZIP
      const zlib = require('zlib');
      payload = zlib.gunzipSync(payload);
    }

    // 解析JSON
    const jsonData = JSON.parse(payload.toString('utf8'));

    return {
      type: 'server_event',
      eventId,
      sessionId,
      data: jsonData
    };
  } catch (error) {
    console.error('解析服务器响应失败:', error);
    return null;
  }
}

/**
 * 解析音频响应
 */
function parseAudioResponse(data: Buffer, header: any): any {
  try {
    // 跳过协议头和可选字段，直接获取音频数据
    let offset = 4;

    // 跳过事件ID
    offset += 4;

    // 跳过session ID
    const sessionIdSize = data.readUInt32LE(offset);
    offset += 4 + sessionIdSize;

    // 获取音频数据长度
    const payloadSize = data.readUInt32LE(offset);
    offset += 4;

    // 获取音频数据
    const audioData = data.subarray(offset, offset + payloadSize);

    return {
      type: 'audio_data',
      eventId: ServerEventId.TTS_RESPONSE,
      data: audioData
    };
  } catch (error) {
    console.error('解析音频响应失败:', error);
    return null;
  }
}

/**
 * 解析错误响应
 */
function parseErrorResponse(data: Buffer, header: any): any {
  try {
    // 类似于服务器响应的解析
    let offset = 4;

    // 错误码
    const errorCode = data.readUInt32LE(offset);
    offset += 4;

    // payload
    const payloadSize = data.readUInt32LE(offset);
    offset += 4;
    const payload = data.subarray(offset, offset + payloadSize);

    const errorData = JSON.parse(payload.toString('utf8'));

    return {
      type: 'error',
      errorCode,
      data: errorData
    };
  } catch (error) {
    console.error('解析错误响应失败:', error);
    return null;
  }
}

/**
 * 创建到豆包API的WebSocket连接
 */
async function createDoubaoConnection(clientId: string): Promise<WebSocket> {
  console.log(`为客户端 ${clientId} 创建豆包API连接`);

  const doubaoWs = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue', {
    headers: {
      'X-Api-App-ID': DOUBAO_CONFIG.appId,
      'X-Api-Access-Key': DOUBAO_CONFIG.accessToken,
      'X-Api-Resource-Id': DOUBAO_CONFIG.resourceId,
      'X-Api-App-Key': DOUBAO_CONFIG.appKey, // 添加必需的App Key
      'X-Api-Connect-Id': `connect-${clientId}`, // 连接追踪ID
      'User-Agent': 'DoubaoRealtimeClient/1.0'
    }
  });

  return new Promise((resolve, reject) => {
    const connectionInfo: ConnectionInfo = {
      ws: doubaoWs,
      clientCallbacks: new Set(),
      lastActivity: Date.now()
    };

    doubaoWs.on('open', () => {
      console.log(`豆包API连接已建立 - 客户端: ${clientId}`);
      activeConnections.set(clientId, connectionInfo);
      resolve(doubaoWs);
    });

    doubaoWs.on('message', (data: Buffer) => {
      // 更新活动时间
      connectionInfo.lastActivity = Date.now();

      // 解析豆包API消息
      const parsedMessage = parseDoubaoMessage(data);
      if (parsedMessage) {
        console.log(`收到豆包API消息 - 客户端: ${clientId}`, parsedMessage.type);

        // 通知所有注册的回调
        connectionInfo.clientCallbacks.forEach(callback => {
          try {
            callback(parsedMessage);
          } catch (error) {
            console.error('回调执行失败:', error);
          }
        });
      }
    });

    doubaoWs.on('error', (error) => {
      console.error(`豆包API连接错误 - 客户端: ${clientId}`, error);
      activeConnections.delete(clientId);
      reject(error);
    });

    doubaoWs.on('close', (code, reason) => {
      console.log(`豆包API连接关闭 - 客户端: ${clientId}`, code, reason);
      activeConnections.delete(clientId);
    });
  });
}

/**
 * POST - WebSocket代理操作
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, clientId } = body;

    if (action === 'connect') {
      // 检查是否已存在连接
      if (activeConnections.has(clientId)) {
        return new Response(JSON.stringify({
          success: true,
          message: '连接已存在',
          clientId,
          status: 'connected'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 创建到豆包API的连接
      const doubaoWs = await createDoubaoConnection(clientId);

      return new Response(JSON.stringify({
        success: true,
        message: '豆包API连接已建立',
        clientId,
        status: 'connected'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (action === 'send') {
      // 转发消息到豆包API
      const { data } = body;
      const connectionInfo = activeConnections.get(clientId);

      if (!connectionInfo || connectionInfo.ws.readyState !== WebSocket.OPEN) {
        return new Response(JSON.stringify({
          success: false,
          error: '豆包API连接不可用'
        }), { status: 400 });
      }

      // 更新活动时间
      connectionInfo.lastActivity = Date.now();

      // 发送数据到豆包API
      if (typeof data === 'string') {
        // 直接发送字符串（用于JSON消息）
        connectionInfo.ws.send(data);
      } else {
        // 解码base64并发送二进制数据
        const buffer = Buffer.from(data, 'base64');
        connectionInfo.ws.send(buffer);
      }

      console.log(`消息已发送到豆包API - 客户端: ${clientId}, 数据长度: ${typeof data === 'string' ? data.length : Buffer.from(data, 'base64').length}`);

      return new Response(JSON.stringify({
        success: true,
        message: '消息已发送到豆包API'
      }));
    }

    if (action === 'listen') {
      // 注册消息监听器（用于接收豆包API的响应）
      const connectionInfo = activeConnections.get(clientId);

      if (!connectionInfo) {
        return new Response(JSON.stringify({
          success: false,
          error: '连接不存在'
        }), { status: 400 });
      }

      // 创建Server-Sent Events响应
      const encoder = new TextEncoder();
      const stream = new ReadableStream({
        start(controller) {
          const callback = (message: any) => {
            const data = `data: ${JSON.stringify(message)}\n\n`;
            controller.enqueue(encoder.encode(data));
          };

          // 注册回调
          connectionInfo.clientCallbacks.add(callback);

          // 发送初始连接确认
          const initMessage = `data: ${JSON.stringify({
            type: 'connection_ready',
            clientId,
            timestamp: Date.now()
          })}\n\n`;
          controller.enqueue(encoder.encode(initMessage));

          // 清理函数
          const cleanup = () => {
            connectionInfo.clientCallbacks.delete(callback);
          };

          // 监听连接关闭
          request.signal.addEventListener('abort', cleanup);

          // 设置定时清理（防止内存泄漏）
          setTimeout(() => {
            cleanup();
            controller.close();
          }, 30 * 60 * 1000); // 30分钟后自动关闭
        }
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control'
        }
      });
    }

    if (action === 'disconnect') {
      // 断开豆包API连接
      const connectionInfo = activeConnections.get(clientId);
      if (connectionInfo) {
        connectionInfo.ws.close();
        activeConnections.delete(clientId);
      }

      return new Response(JSON.stringify({
        success: true,
        message: '连接已断开'
      }));
    }

    return new Response(JSON.stringify({
      success: false,
      error: '未知操作'
    }), { status: 400 });

  } catch (error) {
    console.error('WebSocket代理错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), { status: 500 });
  }
}

/**
 * GET - 获取连接状态
 */
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const clientId = url.searchParams.get('clientId');

  if (clientId) {
    const connectionInfo = activeConnections.get(clientId);
    const isConnected = connectionInfo && connectionInfo.ws.readyState === WebSocket.OPEN;

    return new Response(JSON.stringify({
      clientId,
      connected: isConnected,
      lastActivity: connectionInfo?.lastActivity,
      activeConnections: activeConnections.size,
      callbackCount: connectionInfo?.clientCallbacks.size || 0
    }));
  }

  return new Response(JSON.stringify({
    message: 'WebSocket代理服务 - 豆包实时语音API',
    activeConnections: activeConnections.size,
    config: {
      doubaoApiUrl: 'wss://openspeech.bytedance.com/api/v3/realtime/dialogue',
      proxyEnabled: true,
      supportedActions: ['connect', 'send', 'listen', 'disconnect'],
      features: [
        '二进制协议解析',
        'Server-Sent Events支持',
        '自动连接清理',
        '消息回调管理'
      ]
    },
    documentation: {
      connect: 'POST with action: "connect", clientId',
      send: 'POST with action: "send", clientId, data (base64 or string)',
      listen: 'POST with action: "listen", clientId (returns SSE stream)',
      disconnect: 'POST with action: "disconnect", clientId',
      status: 'GET with optional clientId parameter'
    }
  }));
}