/**
 * 豆包实时语音API代理路由
 * 后端配置API密钥，避免前端暴露敏感信息
 */

import { NextRequest } from 'next/server';

// 豆包API配置 - 优先从环境变量读取，否则使用默认配置
const DOUBAO_CONFIG = {
  appId: process.env.DOUBAO_APP_ID || '2919022726',
  accessToken: process.env.DOUBAO_ACCESS_TOKEN || 'EDQbT-FiT72TPSeOMnePDef1OFKYCAp5',
  resourceId: process.env.DOUBAO_RESOURCE_ID || 'volc.speech.dialog'
};

/**
 * 处理GET请求 - 返回配置信息（不包含敏感数据）
 */
export async function GET(request: NextRequest) {
  try {
    // 返回客户端需要的配置信息（不包含敏感的API密钥）
    return Response.json({
      success: true,
      config: {
        resourceId: DOUBAO_CONFIG.resourceId,
        supportedModes: ['realtime', 'traditional'],
        audioFormats: {
          input: {
            format: 'pcm',
            channels: 1,
            sampleRate: 16000,
            sampleFormat: 'int16',
            endian: 'little'
          },
          output: {
            format: 'pcm', // 或 'ogg'
            channels: 1,
            sampleRate: 24000,
            sampleFormat: 'float32',
            endian: 'little'
          }
        },
        wsUrl: 'wss://openspeech.bytedance.com/api/v3/realtime/dialogue'
      }
    });

  } catch (error) {
    console.error('Error in GET route:', error);
    return Response.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * 处理POST请求 - 验证连接和启动会话
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    switch (body.action) {
      case 'validateConnection':
        // 验证豆包API连接
        return Response.json({
          success: true,
          message: '连接配置有效',
          appId: DOUBAO_CONFIG.appId // 只返回非敏感信息
        });

      case 'getConnectionInfo':
        // 返回WebSocket连接所需的信息
        return Response.json({
          wsUrl: 'wss://openspeech.bytedance.com/api/v3/realtime/dialogue',
          headers: {
            'X-Api-App-ID': DOUBAO_CONFIG.appId,
            'X-Api-Access-Key': DOUBAO_CONFIG.accessToken,
            'X-Api-Resource-Id': DOUBAO_CONFIG.resourceId
          }
        });

      default:
        return Response.json(
          { error: 'Unknown action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in POST route:', error);
    return Response.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * 处理OPTIONS请求（CORS预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}
