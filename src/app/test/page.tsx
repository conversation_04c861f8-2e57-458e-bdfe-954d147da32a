'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { runBackendTests } from '@/test/backend-test';

interface TestResult {
  backendConnection: boolean;
  doubaoConnection: boolean;
  messageListener: boolean;
  clientSDK: boolean;
}

export default function TestPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [backendStatus, setBackendStatus] = useState<any>(null);

  // 检查后端状态
  useEffect(() => {
    const checkBackendStatus = async () => {
      try {
        const response = await fetch('/api/realtime/ws');
        const status = await response.json();
        setBackendStatus(status);
      } catch (error) {
        console.error('检查后端状态失败:', error);
      }
    };

    checkBackendStatus();
  }, []);

  // 重写console.log来捕获测试日志
  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;

  const captureConsoleLogs = () => {
    console.log = (...args) => {
      setLogs(prev => [...prev, `[LOG] ${args.join(' ')}`]);
      originalConsoleLog(...args);
    };

    console.error = (...args) => {
      setLogs(prev => [...prev, `[ERROR] ${args.join(' ')}`]);
      originalConsoleError(...args);
    };
  };

  const restoreConsoleLogs = () => {
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
  };

  const runTests = async () => {
    setIsRunning(true);
    setResults(null);
    setLogs([]);

    captureConsoleLogs();

    try {
      const testResults = await runBackendTests();
      setResults(testResults);
    } catch (error) {
      console.error('测试运行失败:', error);
    } finally {
      restoreConsoleLogs();
      setIsRunning(false);
    }
  };

  const getStatusBadge = (status: boolean | undefined) => {
    if (status === undefined) return <Badge variant="secondary">未测试</Badge>;
    return status ? <Badge variant="default">✅ 通过</Badge> : <Badge variant="destructive">❌ 失败</Badge>;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">豆包实时语音API后端测试</h1>
        <p className="text-muted-foreground">测试后端代理服务的各项功能</p>
      </div>

      {/* 后端状态 */}
      <Card>
        <CardHeader>
          <CardTitle>后端状态</CardTitle>
        </CardHeader>
        <CardContent>
          {backendStatus ? (
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>服务状态:</span>
                <Badge variant="default">✅ 运行中</Badge>
              </div>
              <div className="flex justify-between">
                <span>活跃连接:</span>
                <span>{backendStatus.activeConnections || 0}</span>
              </div>
              <div className="flex justify-between">
                <span>代理启用:</span>
                <Badge variant={backendStatus.config?.proxyEnabled ? "default" : "destructive"}>
                  {backendStatus.config?.proxyEnabled ? "✅ 是" : "❌ 否"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>支持的操作:</span>
                <div className="flex gap-1">
                  {backendStatus.config?.supportedActions?.map((action: string) => (
                    <Badge key={action} variant="outline" className="text-xs">
                      {action}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">
              正在检查后端状态...
            </div>
          )}
        </CardContent>
      </Card>

      {/* 测试控制 */}
      <Card>
        <CardHeader>
          <CardTitle>测试控制</CardTitle>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={runTests} 
            disabled={isRunning}
            className="w-full"
          >
            {isRunning ? '正在运行测试...' : '开始测试'}
          </Button>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      {results && (
        <Card>
          <CardHeader>
            <CardTitle>测试结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>后端连接测试</span>
                {getStatusBadge(results.backendConnection)}
              </div>
              <div className="flex justify-between items-center">
                <span>豆包API连接测试</span>
                {getStatusBadge(results.doubaoConnection)}
              </div>
              <div className="flex justify-between items-center">
                <span>消息监听测试</span>
                {getStatusBadge(results.messageListener)}
              </div>
              <div className="flex justify-between items-center">
                <span>客户端SDK测试</span>
                {getStatusBadge(results.clientSDK)}
              </div>
              
              <div className="pt-3 border-t">
                <div className="flex justify-between items-center font-semibold">
                  <span>总体结果</span>
                  <Badge variant={
                    Object.values(results).every(Boolean) ? "default" : 
                    Object.values(results).some(Boolean) ? "secondary" : "destructive"
                  }>
                    {Object.values(results).filter(Boolean).length}/{Object.keys(results).length} 通过
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 测试日志 */}
      {logs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>测试日志</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg max-h-96 overflow-y-auto">
              <pre className="text-sm whitespace-pre-wrap">
                {logs.join('\n')}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 文档链接 */}
      <Card>
        <CardHeader>
          <CardTitle>相关文档</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div>
              <a 
                href="/BACKEND_IMPLEMENTATION.md" 
                target="_blank"
                className="text-blue-600 hover:underline"
              >
                📖 后端实现文档
              </a>
            </div>
            <div>
              <a 
                href="https://www.volcengine.com/docs/6561/1594356" 
                target="_blank"
                className="text-blue-600 hover:underline"
              >
                🔗 豆包API官方文档
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
