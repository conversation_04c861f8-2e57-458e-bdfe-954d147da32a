import { VoiceChat } from '@/components/voice-chat';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            豆包实时语音对话
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            基于豆包端到端实时语音大模型API的语音交互应用
          </p>
        </header>

        <main>
          <VoiceChat />
        </main>

        <footer className="text-center mt-12 text-sm text-gray-500 dark:text-gray-400">
          <p>
            使用豆包实时语音大模型API构建 |
            <a
              href="https://www.volcengine.com/docs/6561/1594356"
              target="_blank"
              rel="noopener noreferrer"
              className="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              查看API文档
            </a>
          </p>
        </footer>
      </div>
    </div>
  );
}
