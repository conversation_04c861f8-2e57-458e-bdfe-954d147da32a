'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>c<PERSON>ff, Settings } from 'lucide-react';
import { AudioRecorder, AudioPlayer, detectAudioFormat } from '@/lib/audio-utils';
import { DoubaoRealtimeClient, DoubaoConfig, ConnectionState as DoubaoConnectionState, SessionState as DoubaoSessionState } from '@/lib/doubao-client';

// 连接状态类型
type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'error';
type SessionState = 'idle' | 'starting' | 'active' | 'ending' | 'error';

// 消息类型
interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  audioData?: ArrayBuffer;
}

// 配置接口
interface VoiceChatConfig {
  botName?: string;
  strictAudit?: boolean;
}

/**
 * 语音聊天组件
 */
export function VoiceChat() {
  // 状态管理
  const [connectionState, setConnectionState] = useState<ConnectionState>('disconnected');
  const [sessionState, setSessionState] = useState<SessionState>('idle');
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [volume, setVolume] = useState(0);
  const [isRealTimeMode, setIsRealTimeMode] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [config, setConfig] = useState<VoiceChatConfig>({
    botName: '豆包',
    strictAudit: true
  });
  const [showConfig, setShowConfig] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [backendStatus, setBackendStatus] = useState<'checking' | 'ready' | 'error'>('checking');

  // Refs
  const doubaoClientRef = useRef<DoubaoRealtimeClient | null>(null);
  const audioRecorderRef = useRef<AudioRecorder | null>(null);
  const audioPlayerRef = useRef<AudioPlayer | null>(null);
  const sessionIdRef = useRef<string | null>(null);

  // 消息ID计数器
  const messageIdRef = useRef(0);

  // 添加消息函数
  const addMessage = useCallback((type: 'user' | 'assistant' | 'system', content: string) => {
    messageIdRef.current += 1;
    const message: ChatMessage = {
      id: `${Date.now()}-${messageIdRef.current}`,
      type,
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
  }, []);

  // 检查后端状态
  useEffect(() => {
    const checkBackendStatus = async () => {
      try {
        const response = await fetch('/api/realtime');
        if (response.ok) {
          setBackendStatus('ready');
          addMessage('system', '后端API配置就绪');
        } else {
          setBackendStatus('error');
          addMessage('system', '后端API配置错误');
        }
      } catch (error) {
        setBackendStatus('error');
        addMessage('system', '无法连接到后端服务');
      }
    };

    checkBackendStatus();
  }, [addMessage]);

  // 初始化音频组件
  useEffect(() => {
    const initAudio = async () => {
      try {
        audioRecorderRef.current = new AudioRecorder();
        await audioRecorderRef.current.initialize();

        audioPlayerRef.current = new AudioPlayer();
        await audioPlayerRef.current.initialize();
      } catch (error) {
        console.error('Failed to initialize audio:', error);
        addMessage('system', '音频初始化失败，请检查麦克风权限');
      }
    };

    initAudio();

    return () => {
      audioRecorderRef.current?.cleanup();
      audioPlayerRef.current?.cleanup();
    };
  }, [addMessage]);

  // 处理服务器事件
  const handleServerEvent = useCallback(async (event: any) => {
    console.log('收到服务器事件:', event);

    switch (event.eventId) {
      case 450: // ASR_INFO
        if (event.data?.firstWord) {
          setCurrentTranscript('正在识别...');
        }
        break;

      case 451: // ASR_RESPONSE
        if (event.data?.results?.[0]?.text) {
          const text = event.data.results[0].text;
          setCurrentTranscript('');
          addMessage('user', text);
        }
        break;

      case 550: // CHAT_RESPONSE
        if (event.data?.content) {
          addMessage('assistant', event.data.content);
        }
        break;

      case 352: // TTS_RESPONSE
        addMessage('system', '收到TTS音频响应（演示模式）');
        break;

      case 359: // TTS_ENDED
        setIsPlaying(false);
        break;

      default:
        console.log('未处理的服务器事件:', event.eventId, event);
    }
  }, [addMessage]);

  // 连接到豆包API
  const connect = useCallback(async () => {
    try {
      setConnectionState('connecting');
      addMessage('system', '正在连接到豆包API...');
      
      const client = new DoubaoRealtimeClient();
      doubaoClientRef.current = client;

      client.on('connectionStateChange', (state: DoubaoConnectionState) => {
        setConnectionState(state);
        if (state === 'connected') {
          addMessage('system', '已连接到豆包API');
        } else if (state === 'disconnected') {
          addMessage('system', '连接已断开');
          setSessionState('idle');
        } else if (state === 'error') {
          addMessage('system', '连接错误');
        }
      });

      client.on('serverEvent', handleServerEvent);
      client.on('error', (error: Error) => {
        addMessage('system', `错误: ${error.message}`);
      });

      await client.connect();

    } catch (error) {
      setConnectionState('error');
      addMessage('system', '连接失败: ' + (error as Error).message);
      console.error('Connection error:', error);
    }
  }, [addMessage, handleServerEvent]);

  // 断开连接
  const disconnect = useCallback(async () => {
    if (doubaoClientRef.current) {
      await doubaoClientRef.current.disconnect();
      doubaoClientRef.current = null;
    }
    setConnectionState('disconnected');
    setSessionState('idle');
  }, []);

  // 开始会话
  const startSession = useCallback(async () => {
    if (connectionState !== 'connected' || !doubaoClientRef.current) {
      return;
    }

    try {
      setSessionState('starting');
      
      const sessionId = await doubaoClientRef.current.startSession({
        botName: config.botName,
        strictAudit: config.strictAudit
      });

      setSessionState('active');
      sessionIdRef.current = sessionId;
      addMessage('system', '会话已启动');

    } catch (error) {
      setSessionState('error');
      addMessage('system', '会话启动失败');
      console.error('Session start error:', error);
    }
  }, [connectionState, config, addMessage]);

  // 停止实时对话模式
  const stopRealTimeMode = useCallback(() => {
    if (!audioRecorderRef.current) {
      return;
    }

    audioRecorderRef.current.stopRecording();
    setIsRealTimeMode(false);
    setIsRecording(false);
    setIsSpeaking(false);
    setVolume(0);
    setCurrentTranscript('');

    addMessage('system', '实时对话模式已停止');
  }, [addMessage]);

  // 结束会话
  const endSession = useCallback(() => {
    if (sessionState !== 'active' || !doubaoClientRef.current) {
      return;
    }

    if (isRealTimeMode) {
      stopRealTimeMode();
    }

    doubaoClientRef.current.endSession();
    setSessionState('idle');
    sessionIdRef.current = null;
    setCurrentTranscript('');
    
    addMessage('system', '会话已结束');
  }, [sessionState, isRealTimeMode, stopRealTimeMode, addMessage]);

  // 开始实时对话模式
  const startRealTimeMode = useCallback(() => {
    if (!audioRecorderRef.current || sessionState !== 'active') {
      return;
    }

    try {
      setIsRealTimeMode(true);
      setIsRecording(true);
      
      audioRecorderRef.current.setVADConfig(0.01, 1500);
      
      audioRecorderRef.current.startContinuousRecording(
        async (audioData) => {
          if (doubaoClientRef.current) {
            try {
              doubaoClientRef.current.sendAudio(audioData);
            } catch (error) {
              console.error('发送音频失败:', error);
            }
          }
        },
        (speaking, vol) => {
          setIsSpeaking(speaking);
          setVolume(vol * 100);
          
          if (speaking && isPlaying && audioPlayerRef.current) {
            audioPlayerRef.current.stop();
            setIsPlaying(false);
            addMessage('system', '检测到用户说话，已打断AI播放');
          }
        }
      );

      addMessage('system', '实时对话模式已启动，可以开始说话');
    } catch (error) {
      setIsRealTimeMode(false);
      setIsRecording(false);
      addMessage('system', '实时对话启动失败');
      console.error('Real-time mode start error:', error);
    }
  }, [sessionState, isPlaying, addMessage]);

  // 发送打招呼
  const sayHello = useCallback(() => {
    if (sessionState !== 'active' || !doubaoClientRef.current) {
      return;
    }

    // 发送TTS文本
    doubaoClientRef.current.sendTTSText('你好', true, true);
    addMessage('user', '你好');
  }, [sessionState, addMessage]);

  // 渲染配置面板
  const renderConfigPanel = () => (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          对话配置
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="p-3 bg-green-50 rounded-lg">
          <p className="text-sm text-green-800">
            ✅ API配置已在后端设置，无需手动输入密钥
          </p>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">机器人名称</label>
          <input
            type="text"
            value={config.botName}
            onChange={(e) => setConfig(prev => ({ ...prev, botName: e.target.value }))}
            className="w-full p-2 border rounded"
            placeholder="豆包"
          />
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="strictAudit"
            checked={config.strictAudit}
            onChange={(e) => setConfig(prev => ({ ...prev, strictAudit: e.target.checked }))}
          />
          <label htmlFor="strictAudit" className="text-sm">严格审核模式</label>
        </div>
        <Button
          onClick={() => setShowConfig(false)}
          className="w-full"
        >
          确认配置
        </Button>
      </CardContent>
    </Card>
  );

  if (showConfig) {
    return renderConfigPanel();
  }

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-4">
      {/* 状态栏 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Badge variant={backendStatus === 'ready' ? 'default' : backendStatus === 'error' ? 'destructive' : 'secondary'}>
                后端: {backendStatus === 'checking' ? '检查中' : backendStatus === 'ready' ? '就绪' : '错误'}
              </Badge>
              <Badge variant={connectionState === 'connected' ? 'default' : 'secondary'}>
                连接: {connectionState}
              </Badge>
              <Badge variant={sessionState === 'active' ? 'default' : 'secondary'}>
                会话: {sessionState}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowConfig(true)}
              >
                <Settings className="w-4 h-4" />
              </Button>
              {connectionState === 'disconnected' ? (
                <Button
                  onClick={connect}
                  disabled={backendStatus !== 'ready'}
                >
                  连接
                </Button>
              ) : (
                <Button variant="outline" onClick={disconnect}>断开</Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 控制面板 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center gap-6">
            {sessionState === 'idle' && connectionState === 'connected' && (
              <Button onClick={startSession} size="lg">开始会话</Button>
            )}

            {sessionState === 'active' && (
              <>
                <div className="flex items-center gap-4">
                  {!isRealTimeMode ? (
                    <Button
                      onClick={startRealTimeMode}
                      size="lg"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      开启实时对话
                    </Button>
                  ) : (
                    <Button
                      onClick={stopRealTimeMode}
                      size="lg"
                      variant="destructive"
                    >
                      停止实时对话
                    </Button>
                  )}

                  <Button variant="outline" onClick={sayHello}>
                    打招呼
                  </Button>

                  <Button variant="outline" onClick={endSession}>
                    结束会话
                  </Button>
                </div>

                {isRealTimeMode && (
                  <div className="w-full max-w-md">
                    <div className="flex items-center justify-center gap-4 mb-4">
                      <div className={`w-4 h-4 rounded-full ${isSpeaking ? 'bg-red-500 animate-pulse' : 'bg-gray-300'}`}></div>
                      <span className="text-sm font-medium">
                        {isSpeaking ? '正在说话' : '等待语音输入'}
                      </span>
                      <div className={`w-4 h-4 rounded-full ${isPlaying ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'}`}></div>
                      <span className="text-sm font-medium">
                        {isPlaying ? 'AI播放中' : 'AI待机'}
                      </span>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>音量</span>
                        <span>{Math.round(volume)}%</span>
                      </div>
                      <Progress value={volume} className="w-full h-2" />
                    </div>

                    {currentTranscript && (
                      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm text-blue-800">识别中: {currentTranscript}</p>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 消息列表 */}
      <Card>
        <CardHeader>
          <CardTitle>对话记录</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`p-3 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-blue-100 ml-8'
                    : message.type === 'assistant'
                    ? 'bg-gray-100 mr-8'
                    : 'bg-yellow-50 text-center'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {message.type === 'user' ? '用户' :
                     message.type === 'assistant' ? '豆包' : '系统'}
                  </span>
                  <span className="text-xs text-gray-500">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <p className="mt-1">{message.content}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
