/**
 * 豆包实时语音大模型API二进制协议处理
 * 基于官方文档实现WebSocket二进制协议
 */

// 消息类型定义
export enum MessageType {
  FULL_CLIENT_REQUEST = 0b0001,    // 客户端发送文本事件
  FULL_SERVER_RESPONSE = 0b1001,   // 服务器返回文本事件
  AUDIO_ONLY_REQUEST = 0b0010,     // 客户端发送音频数据
  AUDIO_ONLY_RESPONSE = 0b1011,    // 服务器返回音频数据
  ERROR_INFORMATION = 0b1111       // 服务器返回错误事件
}

// 序列化方法
export enum SerializationMethod {
  RAW = 0b0000,   // 无特殊序列化，主要针对二进制音频数据
  JSON = 0b0001   // 主要针对文本类型消息
}

// 压缩方法
export enum CompressionMethod {
  NONE = 0b0000,  // 无压缩（推荐）
  GZIP = 0b0001   // gzip压缩
}

// 消息类型特定标志
export enum MessageTypeSpecificFlags {
  NO_SEQUENCE = 0b0000,           // 没有sequence字段
  NON_TERMINAL_SEQUENCE = 0b0001, // 序号大于0的非终端数据包
  LAST_NO_SEQUENCE = 0b0010,      // 最后一个无序号的数据包
  LAST_NEGATIVE_SEQUENCE = 0b0011, // 最后一个序号小于0的数据包
  HAS_EVENT = 0b0100              // 携带事件ID
}

// 客户端事件ID
export enum ClientEventId {
  START_CONNECTION = 1,
  FINISH_CONNECTION = 2,
  START_SESSION = 100,
  FINISH_SESSION = 102,
  TASK_REQUEST = 200,
  SAY_HELLO = 300,
  CHAT_TTS_TEXT = 500
}

// 服务端事件ID
export enum ServerEventId {
  CONNECTION_STARTED = 50,
  CONNECTION_FAILED = 51,
  CONNECTION_FINISHED = 52,
  SESSION_STARTED = 150,
  SESSION_FINISHED = 152,
  SESSION_FAILED = 153,
  TTS_SENTENCE_START = 350,
  TTS_SENTENCE_END = 351,
  TTS_RESPONSE = 352,
  TTS_ENDED = 359,
  ASR_INFO = 450,
  ASR_RESPONSE = 451,
  ASR_ENDED = 459,
  CHAT_RESPONSE = 550,
  CHAT_ENDED = 559
}

// 协议头结构
export interface ProtocolHeader {
  protocolVersion: number;  // 协议版本，目前固定为1
  headerSize: number;       // 头部大小，固定为4字节
  messageType: MessageType;
  messageTypeSpecificFlags: MessageTypeSpecificFlags;
  serializationMethod: SerializationMethod;
  compressionMethod: CompressionMethod;
  reserved: number;         // 保留字段，固定为0x00
}

// 可选字段结构
export interface OptionalFields {
  errorCode?: number;
  sequence?: number;
  eventId?: number;
  connectIdSize?: number;
  connectId?: string;
  sessionIdSize?: number;
  sessionId?: string;
}

// 完整消息结构
export interface ProtocolMessage {
  header: ProtocolHeader;
  optional: OptionalFields;
  payloadSize: number;
  payload: Buffer;
}

/**
 * 创建协议头
 */
export function createHeader(
  messageType: MessageType,
  messageTypeSpecificFlags: MessageTypeSpecificFlags,
  serializationMethod: SerializationMethod = SerializationMethod.JSON,
  compressionMethod: CompressionMethod = CompressionMethod.NONE
): ProtocolHeader {
  return {
    protocolVersion: 1,
    headerSize: 1,
    messageType,
    messageTypeSpecificFlags,
    serializationMethod,
    compressionMethod,
    reserved: 0
  };
}

/**
 * 编码协议头为4字节Buffer
 */
export function encodeHeader(header: ProtocolHeader): Buffer {
  const buffer = Buffer.alloc(4);
  
  // Byte 0: Protocol Version (left 4 bits) + Header Size (right 4 bits)
  buffer[0] = (header.protocolVersion << 4) | header.headerSize;
  
  // Byte 1: Message Type (left 4 bits) + Message Type Specific Flags (right 4 bits)
  buffer[1] = (header.messageType << 4) | header.messageTypeSpecificFlags;
  
  // Byte 2: Serialization Method (left 4 bits) + Compression Method (right 4 bits)
  buffer[2] = (header.serializationMethod << 4) | header.compressionMethod;
  
  // Byte 3: Reserved
  buffer[3] = header.reserved;
  
  return buffer;
}

/**
 * 解码协议头
 */
export function decodeHeader(buffer: Buffer): ProtocolHeader {
  if (buffer.length < 4) {
    throw new Error('Header buffer must be at least 4 bytes');
  }
  
  return {
    protocolVersion: (buffer[0] >> 4) & 0x0F,
    headerSize: buffer[0] & 0x0F,
    messageType: (buffer[1] >> 4) & 0x0F,
    messageTypeSpecificFlags: buffer[1] & 0x0F,
    serializationMethod: (buffer[2] >> 4) & 0x0F,
    compressionMethod: buffer[2] & 0x0F,
    reserved: buffer[3]
  };
}

/**
 * 编码可选字段
 */
export function encodeOptionalFields(optional: OptionalFields): Buffer {
  const buffers: Buffer[] = [];
  
  // 错误码 (4字节)
  if (optional.errorCode !== undefined) {
    const errorCodeBuffer = Buffer.alloc(4);
    errorCodeBuffer.writeUInt32LE(optional.errorCode, 0);
    buffers.push(errorCodeBuffer);
  }
  
  // 序列号 (4字节)
  if (optional.sequence !== undefined) {
    const sequenceBuffer = Buffer.alloc(4);
    sequenceBuffer.writeInt32LE(optional.sequence, 0);
    buffers.push(sequenceBuffer);
  }
  
  // 事件ID (4字节)
  if (optional.eventId !== undefined) {
    const eventIdBuffer = Buffer.alloc(4);
    eventIdBuffer.writeUInt32LE(optional.eventId, 0);
    buffers.push(eventIdBuffer);
  }
  
  // Connect ID
  if (optional.connectIdSize !== undefined && optional.connectId !== undefined) {
    const connectIdSizeBuffer = Buffer.alloc(4);
    connectIdSizeBuffer.writeUInt32LE(optional.connectIdSize, 0);
    buffers.push(connectIdSizeBuffer);
    
    const connectIdBuffer = Buffer.from(optional.connectId, 'utf8');
    buffers.push(connectIdBuffer);
  }
  
  // Session ID
  if (optional.sessionIdSize !== undefined && optional.sessionId !== undefined) {
    const sessionIdSizeBuffer = Buffer.alloc(4);
    sessionIdSizeBuffer.writeUInt32LE(optional.sessionIdSize, 0);
    buffers.push(sessionIdSizeBuffer);
    
    const sessionIdBuffer = Buffer.from(optional.sessionId, 'utf8');
    buffers.push(sessionIdBuffer);
  }
  
  return Buffer.concat(buffers);
}

/**
 * 编码完整消息
 */
export function encodeMessage(message: ProtocolMessage): Buffer {
  const headerBuffer = encodeHeader(message.header);
  const optionalBuffer = encodeOptionalFields(message.optional);
  
  // Payload size (4字节)
  const payloadSizeBuffer = Buffer.alloc(4);
  payloadSizeBuffer.writeUInt32LE(message.payloadSize, 0);
  
  return Buffer.concat([
    headerBuffer,
    optionalBuffer,
    payloadSizeBuffer,
    message.payload
  ]);
}

/**
 * 创建客户端事件消息（基于Python演示改进）
 */
export function createClientEventMessage(
  eventId: ClientEventId,
  sessionId: string,
  payload: any,
  connectId?: string
): Buffer {
  // 使用GZIP压缩，如Python演示
  const header = createHeader(
    MessageType.FULL_CLIENT_REQUEST,
    MessageTypeSpecificFlags.HAS_EVENT,
    SerializationMethod.JSON,
    CompressionMethod.GZIP
  );

  // JSON序列化
  const jsonBuffer = Buffer.from(JSON.stringify(payload), 'utf8');

  // GZIP压缩（如Python演示）
  const zlib = require('zlib');
  const compressedPayload = zlib.gzipSync(jsonBuffer);

  const optional: OptionalFields = {
    eventId,
    sessionIdSize: Buffer.byteLength(sessionId, 'utf8'),
    sessionId
  };

  if (connectId) {
    optional.connectIdSize = Buffer.byteLength(connectId, 'utf8');
    optional.connectId = connectId;
  }

  const message: ProtocolMessage = {
    header,
    optional,
    payloadSize: compressedPayload.length,
    payload: compressedPayload
  };

  return encodeMessage(message);
}

/**
 * 创建音频数据消息
 */
export function createAudioMessage(audioData: Buffer, sessionId: string): Buffer {
  const header = createHeader(
    MessageType.AUDIO_ONLY_REQUEST,
    MessageTypeSpecificFlags.HAS_EVENT,
    SerializationMethod.RAW
  );
  
  const optional: OptionalFields = {
    eventId: ClientEventId.TASK_REQUEST,
    sessionIdSize: Buffer.byteLength(sessionId, 'utf8'),
    sessionId
  };
  
  const message: ProtocolMessage = {
    header,
    optional,
    payloadSize: audioData.length,
    payload: audioData
  };
  
  return encodeMessage(message);
}
