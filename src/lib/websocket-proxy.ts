/**
 * WebSocket代理服务器 - 处理豆包API的WebSocket连接
 * 解决浏览器WebSocket不支持自定义headers的问题
 */

import WebSocket from 'ws';
import { IncomingMessage } from 'http';
import { Socket } from 'net';

// 豆包API配置
const DOUBAO_CONFIG = {
  appId: process.env.DOUBAO_APP_ID || '2919022726',
  accessToken: process.env.DOUBAO_ACCESS_TOKEN || 'EDQbT-FiT72TPSeOMnePDef1OFKYCAp5',
  resourceId: process.env.DOUBAO_RESOURCE_ID || 'volc.speech.dialog'
};

export class WebSocketProxy {
  private wss: WebSocket.Server | null = null;
  private clientConnections = new Map<WebSocket, WebSocket>();

  /**
   * 启动WebSocket代理服务器
   */
  start(server: any) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/api/realtime/ws'
    });

    this.wss.on('connection', (clientWs: WebSocket, request: IncomingMessage) => {
      console.log('客户端WebSocket连接建立');
      this.handleClientConnection(clientWs, request);
    });

    console.log('WebSocket代理服务器已启动');
  }

  /**
   * 处理客户端连接
   */
  private async handleClientConnection(clientWs: WebSocket, request: IncomingMessage) {
    try {
      // 创建到豆包API的WebSocket连接
      const doubaoWs = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue', {
        headers: {
          'X-Api-App-ID': DOUBAO_CONFIG.appId,
          'X-Api-Access-Key': DOUBAO_CONFIG.accessToken,
          'X-Api-Resource-Id': DOUBAO_CONFIG.resourceId,
          'User-Agent': 'DoubaoRealtimeClient/1.0',
          'Origin': 'https://your-domain.com' // 根据需要设置
        }
      });

      // 保存连接映射
      this.clientConnections.set(clientWs, doubaoWs);

      // 豆包WebSocket事件处理
      doubaoWs.on('open', () => {
        console.log('豆包API WebSocket连接已建立');
        // 通知客户端连接成功
        if (clientWs.readyState === WebSocket.OPEN) {
          clientWs.send(JSON.stringify({
            type: 'connection',
            status: 'connected'
          }));
        }
      });

      doubaoWs.on('message', (data: WebSocket.Data) => {
        // 转发豆包API的消息到客户端
        if (clientWs.readyState === WebSocket.OPEN) {
          clientWs.send(data);
        }
      });

      doubaoWs.on('error', (error) => {
        console.error('豆包API WebSocket错误:', error);
        if (clientWs.readyState === WebSocket.OPEN) {
          clientWs.send(JSON.stringify({
            type: 'error',
            message: error.message
          }));
        }
      });

      doubaoWs.on('close', (code, reason) => {
        console.log('豆包API WebSocket连接关闭:', code, reason);
        if (clientWs.readyState === WebSocket.OPEN) {
          clientWs.close(code, reason.toString());
        }
        this.clientConnections.delete(clientWs);
      });

      // 客户端WebSocket事件处理
      clientWs.on('message', (data: WebSocket.Data) => {
        // 转发客户端消息到豆包API
        if (doubaoWs.readyState === WebSocket.OPEN) {
          doubaoWs.send(data);
        }
      });

      clientWs.on('error', (error) => {
        console.error('客户端WebSocket错误:', error);
        if (doubaoWs.readyState === WebSocket.OPEN) {
          doubaoWs.close();
        }
      });

      clientWs.on('close', (code, reason) => {
        console.log('客户端WebSocket连接关闭:', code, reason);
        if (doubaoWs.readyState === WebSocket.OPEN) {
          doubaoWs.close();
        }
        this.clientConnections.delete(clientWs);
      });

    } catch (error) {
      console.error('处理客户端连接失败:', error);
      if (clientWs.readyState === WebSocket.OPEN) {
        clientWs.close(1011, 'Internal server error');
      }
    }
  }

  /**
   * 停止WebSocket代理服务器
   */
  stop() {
    if (this.wss) {
      // 关闭所有连接
      this.clientConnections.forEach((doubaoWs, clientWs) => {
        if (clientWs.readyState === WebSocket.OPEN) {
          clientWs.close();
        }
        if (doubaoWs.readyState === WebSocket.OPEN) {
          doubaoWs.close();
        }
      });
      
      this.clientConnections.clear();
      this.wss.close();
      this.wss = null;
      console.log('WebSocket代理服务器已停止');
    }
  }
}

// 单例实例
export const wsProxy = new WebSocketProxy();
