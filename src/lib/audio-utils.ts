/**
 * 音频处理工具
 * 处理PCM音频录制和OGG/Opus音频播放
 */

// 音频配置常量（基于Python演示优化）
export const AUDIO_CONFIG = {
  // 录音配置 - 符合豆包API要求（基于Python演示）
  RECORDING: {
    sampleRate: 16000,      // 采样率16kHz
    channels: 1,            // 单声道
    bitsPerSample: 16,      // 16位采样
    format: 'int16',        // int16格式
    chunkSize: 3200         // Python演示中的chunk大小
  },
  // 播放配置（基于Python演示）
  PLAYBACK: {
    sampleRate: 24000,      // 播放采样率24kHz
    channels: 1,            // 单声道
    format: 'float32',      // float32格式
    chunkSize: 3200         // Python演示中的chunk大小
  }
} as const;

/**
 * 音频录制器类 - 支持连续录音和VAD
 */
export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;
  private stream: MediaStream | null = null;
  private processor: ScriptProcessorNode | null = null;
  private analyser: AnalyserNode | null = null;
  private isRecording = false;
  private isContinuousMode = false;
  private onDataCallback?: (data: ArrayBuffer) => void;
  private onVADCallback?: (isSpeaking: boolean, volume: number) => void;
  private vadThreshold = 0.01; // VAD阈值
  private silenceTimeout: NodeJS.Timeout | null = null;
  private silenceDuration = 1000; // 1秒静音后认为说话结束

  /**
   * 初始化录音器
   */
  async initialize(): Promise<void> {
    try {
      // 获取麦克风权限
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: AUDIO_CONFIG.RECORDING.sampleRate,
          channelCount: AUDIO_CONFIG.RECORDING.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // 创建音频上下文
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: AUDIO_CONFIG.RECORDING.sampleRate
      });

      // 创建音频源
      const source = this.audioContext.createMediaStreamSource(this.stream);
      
      // 创建分析器节点用于VAD
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 2048;
      this.analyser.smoothingTimeConstant = 0.8;

      // 创建处理器节点
      this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);

      // 连接音频节点
      source.connect(this.analyser);
      this.analyser.connect(this.processor);
      this.processor.connect(this.audioContext.destination);

      // 处理音频数据
      this.processor.onaudioprocess = (event) => {
        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);

        // 计算音量用于VAD
        const volume = this.calculateVolume(inputData);
        const isSpeaking = volume > this.vadThreshold;

        // VAD回调
        if (this.onVADCallback) {
          this.onVADCallback(isSpeaking, volume);
        }

        // 处理静音检测
        this.handleSilenceDetection(isSpeaking);

        // 发送音频数据
        if (this.isRecording && this.onDataCallback) {
          // 转换为16位PCM
          const pcmData = this.floatTo16BitPCM(inputData);
          this.onDataCallback(pcmData.buffer as ArrayBuffer);
        }
      };

    } catch (error) {
      console.error('Failed to initialize audio recorder:', error);
      throw new Error('无法初始化录音器，请检查麦克风权限');
    }
  }

  /**
   * 开始录音
   */
  startRecording(onData: (data: ArrayBuffer) => void, onVAD?: (isSpeaking: boolean, volume: number) => void): void {
    if (!this.audioContext || !this.processor) {
      throw new Error('录音器未初始化');
    }

    this.onDataCallback = onData;
    this.onVADCallback = onVAD;
    this.isRecording = true;

    // 恢复音频上下文
    if (this.audioContext.state === 'suspended') {
      this.audioContext.resume();
    }
  }

  /**
   * 开始连续录音模式
   */
  startContinuousRecording(onData: (data: ArrayBuffer) => void, onVAD?: (isSpeaking: boolean, volume: number) => void): void {
    this.isContinuousMode = true;
    this.startRecording(onData, onVAD);
  }

  /**
   * 停止录音
   */
  stopRecording(): void {
    this.isRecording = false;
    this.isContinuousMode = false;
    this.onDataCallback = undefined;
    this.onVADCallback = undefined;

    if (this.silenceTimeout) {
      clearTimeout(this.silenceTimeout);
      this.silenceTimeout = null;
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopRecording();
    
    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
  }

  /**
   * 将Float32Array转换为16位PCM
   */
  private floatTo16BitPCM(input: Float32Array): Int16Array {
    const output = new Int16Array(input.length);
    for (let i = 0; i < input.length; i++) {
      const sample = Math.max(-1, Math.min(1, input[i]));
      output[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
    }
    return output;
  }

  /**
   * 计算音频音量
   */
  private calculateVolume(audioData: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    return Math.sqrt(sum / audioData.length);
  }

  /**
   * 处理静音检测
   */
  private handleSilenceDetection(isSpeaking: boolean): void {
    if (this.isContinuousMode) {
      if (isSpeaking) {
        // 检测到说话，清除静音计时器
        if (this.silenceTimeout) {
          clearTimeout(this.silenceTimeout);
          this.silenceTimeout = null;
        }
      } else {
        // 检测到静音，开始计时
        if (!this.silenceTimeout) {
          this.silenceTimeout = setTimeout(() => {
            // 静音超时，可以在这里触发说话结束事件
            if (this.onVADCallback) {
              this.onVADCallback(false, 0);
            }
          }, this.silenceDuration);
        }
      }
    }
  }

  /**
   * 设置VAD参数
   */
  setVADConfig(threshold: number, silenceDuration: number): void {
    this.vadThreshold = threshold;
    this.silenceDuration = silenceDuration;
  }

  /**
   * 检查是否正在录音
   */
  get recording(): boolean {
    return this.isRecording;
  }

  /**
   * 检查是否为连续录音模式
   */
  get continuousMode(): boolean {
    return this.isContinuousMode;
  }
}

/**
 * 音频播放器类 - 支持实时播放和打断（基于Python演示改进）
 */
export class AudioPlayer {
  private audioContext: AudioContext | null = null;
  private gainNode: GainNode | null = null;
  private isPlaying = false;
  private currentSources: AudioBufferSourceNode[] = [];
  private audioQueue: ArrayBuffer[] = [];
  private isProcessingQueue = false;
  private queueProcessingInterval: NodeJS.Timeout | null = null;

  /**
   * 初始化播放器
   */
  async initialize(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.gainNode = this.audioContext.createGain();
      this.gainNode.connect(this.audioContext.destination);
    } catch (error) {
      console.error('Failed to initialize audio player:', error);
      throw new Error('无法初始化音频播放器');
    }
  }

  /**
   * 播放OGG/Opus音频数据
   */
  async playOggOpus(audioData: ArrayBuffer): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('播放器未初始化');
    }

    try {
      // 解码音频数据
      const audioBuffer = await this.audioContext.decodeAudioData(audioData.slice(0));
      
      // 创建音频源
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.gainNode);
      
      // 播放音频
      this.isPlaying = true;
      source.start();
      
      // 监听播放结束
      source.onended = () => {
        this.isPlaying = false;
      };
      
    } catch (error) {
      console.error('Failed to play audio:', error);
      this.isPlaying = false;
      throw new Error('音频播放失败');
    }
  }

  /**
   * 播放PCM音频数据
   */
  async playPCM(pcmData: ArrayBuffer, sampleRate: number = AUDIO_CONFIG.PLAYBACK.sampleRate): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('播放器未初始化');
    }

    try {
      // 将PCM数据转换为Float32Array
      const float32Data = new Float32Array(pcmData);
      
      // 创建音频缓冲区
      const audioBuffer = this.audioContext.createBuffer(1, float32Data.length, sampleRate);
      audioBuffer.copyToChannel(float32Data, 0);
      
      // 创建音频源
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.gainNode);
      
      // 播放音频
      this.isPlaying = true;
      source.start();
      
      // 监听播放结束
      source.onended = () => {
        this.isPlaying = false;
      };
      
    } catch (error) {
      console.error('Failed to play PCM audio:', error);
      this.isPlaying = false;
      throw new Error('PCM音频播放失败');
    }
  }

  /**
   * 停止播放（打断功能）
   */
  stop(): void {
    this.isPlaying = false;

    // 停止所有正在播放的音频源
    this.currentSources.forEach(source => {
      try {
        source.stop();
      } catch (error) {
        // 忽略已经停止的源
      }
    });
    this.currentSources = [];

    // 清空音频队列
    this.audioQueue = [];
    this.isProcessingQueue = false;
  }

  /**
   * 添加音频到播放队列
   */
  queueAudio(audioData: ArrayBuffer): void {
    this.audioQueue.push(audioData);
    if (!this.isProcessingQueue) {
      this.processAudioQueue();
    }
  }

  /**
   * 处理音频播放队列
   */
  private async processAudioQueue(): Promise<void> {
    if (this.isProcessingQueue || this.audioQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.audioQueue.length > 0) {
      const audioData = this.audioQueue.shift();
      if (audioData) {
        try {
          await this.playAudioBuffer(audioData);
        } catch (error) {
          console.error('Failed to play queued audio:', error);
        }
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * 播放单个音频缓冲区
   */
  private async playAudioBuffer(audioData: ArrayBuffer): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('播放器未初始化');
    }

    try {
      // 检测音频格式并播放
      const format = detectAudioFormat(audioData);

      if (format === 'ogg') {
        await this.playOggOpusBuffer(audioData);
      } else {
        await this.playPCMBuffer(audioData);
      }
    } catch (error) {
      console.error('Failed to play audio buffer:', error);
      throw error;
    }
  }

  /**
   * 播放OGG/Opus音频缓冲区
   */
  private async playOggOpusBuffer(audioData: ArrayBuffer): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('播放器未初始化');
    }

    return new Promise((resolve, reject) => {
      this.audioContext!.decodeAudioData(audioData.slice(0))
        .then(audioBuffer => {
          const source = this.audioContext!.createBufferSource();
          source.buffer = audioBuffer;
          source.connect(this.gainNode!);

          this.currentSources.push(source);
          this.isPlaying = true;

          source.onended = () => {
            this.isPlaying = false;
            const index = this.currentSources.indexOf(source);
            if (index > -1) {
              this.currentSources.splice(index, 1);
            }
            resolve();
          };

          source.start();
        })
        .catch(reject);
    });
  }

  /**
   * 播放PCM音频缓冲区
   */
  private async playPCMBuffer(audioData: ArrayBuffer, sampleRate: number = AUDIO_CONFIG.PLAYBACK.sampleRate): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error('播放器未初始化');
    }

    return new Promise((resolve, reject) => {
      try {
        let float32Data: Float32Array;

        // 根据文档，服务端返回的PCM是Float32格式
        if (audioData.byteLength % 4 === 0) {
          // 直接使用Float32数据
          float32Data = new Float32Array(audioData);
        } else {
          // 如果不是4字节对齐，可能是Int16格式，需要转换
          const int16Data = new Int16Array(audioData);
          float32Data = new Float32Array(int16Data.length);
          for (let i = 0; i < int16Data.length; i++) {
            float32Data[i] = int16Data[i] / 32768.0; // 转换为-1.0到1.0范围
          }
        }

        // 验证音频数据
        if (float32Data.length === 0) {
          console.warn('PCM音频数据为空');
          resolve();
          return;
        }

        // 创建音频缓冲区
        const audioBuffer = this.audioContext!.createBuffer(1, float32Data.length, sampleRate);
        audioBuffer.copyToChannel(float32Data, 0);

        const source = this.audioContext!.createBufferSource();
        source.buffer = audioBuffer;
        source.connect(this.gainNode!);

        this.currentSources.push(source);
        this.isPlaying = true;

        source.onended = () => {
          this.isPlaying = false;
          const index = this.currentSources.indexOf(source);
          if (index > -1) {
            this.currentSources.splice(index, 1);
          }
          resolve();
        };

        source.start();
        console.log(`播放PCM音频: ${float32Data.length}样本, ${sampleRate}Hz`);

      } catch (error) {
        this.isPlaying = false;
        console.error('PCM音频播放错误:', error);
        reject(error);
      }
    });
  }

  /**
   * 设置音量
   */
  setVolume(volume: number): void {
    if (this.gainNode) {
      this.gainNode.gain.value = Math.max(0, Math.min(1, volume));
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stop();
    
    if (this.gainNode) {
      this.gainNode.disconnect();
      this.gainNode = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }

  /**
   * 检查是否正在播放
   */
  get playing(): boolean {
    return this.isPlaying;
  }
}

/**
 * 音频格式检测
 */
export function detectAudioFormat(data: ArrayBuffer, formatHint?: string): 'ogg' | 'pcm' | 'unknown' {
  // 如果有格式提示，优先使用
  if (formatHint === 'pcm' || formatHint === 'ogg') {
    return formatHint;
  }

  const view = new Uint8Array(data);

  // 检查OGG格式标识
  if (view.length >= 4 &&
      view[0] === 0x4F && view[1] === 0x67 &&
      view[2] === 0x67 && view[3] === 0x53) {
    return 'ogg';
  }

  // 检查WAV格式 (以"RIFF"开头)
  if (view.length >= 4 &&
      view[0] === 0x52 && view[1] === 0x49 &&
      view[2] === 0x46 && view[3] === 0x46) {
    return 'pcm';
  }

  // 检查是否为Float32 PCM数据（根据文档，服务端返回Float32格式）
  if (data.byteLength % 4 === 0) {
    const float32View = new Float32Array(data);
    // 检查是否为合理的音频范围（-1.0 到 1.0）
    let validSamples = 0;
    const sampleCount = Math.min(100, float32View.length); // 检查前100个样本

    for (let i = 0; i < sampleCount; i++) {
      if (float32View[i] >= -1.0 && float32View[i] <= 1.0) {
        validSamples++;
      }
    }

    // 如果大部分样本在有效范围内，认为是PCM
    if (validSamples / sampleCount > 0.8) {
      return 'pcm';
    }
  }

  // 默认假设为PCM
  return 'pcm';
}

/**
 * 创建静音PCM数据
 */
export function createSilentPCM(durationMs: number, sampleRate: number = AUDIO_CONFIG.RECORDING.sampleRate): ArrayBuffer {
  const samples = Math.floor(durationMs * sampleRate / 1000);
  const buffer = new Int16Array(samples);
  // 填充静音（全零）
  buffer.fill(0);
  return buffer.buffer;
}
