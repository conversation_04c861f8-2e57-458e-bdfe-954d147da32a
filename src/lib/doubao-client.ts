/**
 * 豆包实时语音大模型API客户端 - 生产版本
 */

import {
  createClientEventMessage,
  createAudioMessage,
  ClientEventId,
  ServerEventId,
  decodeHeader,
  MessageType
} from './protocol';

// 连接配置
export interface DoubaoConfig {
  resourceId?: string;
  connectId?: string;
}

// 会话配置
export interface SessionConfig {
  botName?: string;
  dialogId?: string;
  strictAudit?: boolean;
  ttsConfig?: {
    audioConfig?: {
      channel: number;
      format: 'pcm' | 'ogg';
      sampleRate: number;
    };
  };
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 会话状态
export enum SessionState {
  IDLE = 'idle',
  STARTING = 'starting',
  ACTIVE = 'active',
  ENDING = 'ending',
  ERROR = 'error'
}

/**
 * 豆包实时语音客户端 - 生产版本
 */
export class DoubaoRealtimeClient {
  private ws: WebSocket | null = null;
  private config: DoubaoConfig;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private sessionState: SessionState = SessionState.IDLE;
  private currentSessionId: string | null = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private messageQueue: Buffer[] = [];
  private isProcessingMessages = false;

  constructor(config: DoubaoConfig = {}) {
    this.config = {
      resourceId: 'volc.speech.dialog',
      ...config
    };
  }

  /**
   * 连接到豆包API（通过HTTP代理）
   */
  async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED) {
      return;
    }

    this.connectionState = ConnectionState.CONNECTING;
    this.emit('connectionStateChange', this.connectionState);

    try {
      // 生成客户端ID
      this.currentSessionId = this.generateSessionId();

      // 通过HTTP API建立到豆包API的连接
      const response = await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'connect',
          clientId: this.currentSessionId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to connect to Doubao API');
      }

      const result = await response.json();
      console.log('豆包API连接成功:', result);

      this.connectionState = ConnectionState.CONNECTED;
      this.emit('connectionStateChange', this.connectionState);

    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChange', this.connectionState);
      throw error;
    }
  }

  /**
   * 设置WebSocket事件处理器
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(event.data);
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭', event.code, event.reason);
      this.connectionState = ConnectionState.DISCONNECTED;
      this.emit('connectionStateChange', this.connectionState);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChange', this.connectionState);
      this.emit('error', error);
    };
  }

  /**
   * 等待WebSocket连接建立
   */
  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.ws) {
        reject(new Error('WebSocket未初始化'));
        return;
      }

      if (this.ws.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('连接超时'));
      }, 10000);

      this.ws.onopen = () => {
        clearTimeout(timeout);
        resolve();
      };

      this.ws.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('连接失败'));
      };
    });
  }

  /**
   * 发送StartConnection事件
   */
  private sendStartConnection(): void {
    const message = createClientEventMessage(
      ClientEventId.START_CONNECTION,
      '',
      {}
    );
    this.sendBinary(message);
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: ArrayBuffer): void {
    try {
      // 检查是否为JSON消息（代理状态消息）
      if (data instanceof ArrayBuffer) {
        const text = new TextDecoder().decode(data);
        try {
          const json = JSON.parse(text);
          if (json.type === 'connection' && json.status === 'connected') {
            console.log('代理连接成功');
            return;
          }
          if (json.type === 'error') {
            console.error('代理错误:', json.message);
            this.emit('error', new Error(json.message));
            return;
          }
        } catch {
          // 不是JSON，继续处理为二进制协议
        }
      }

      // 解析二进制协议
      const buffer = Buffer.from(data);
      const header = decodeHeader(buffer);

      // 根据消息类型处理
      if (header.messageType === MessageType.FULL_SERVER_RESPONSE) {
        this.handleServerResponse(data);
      }
    } catch (error) {
      console.error('处理消息失败:', error);
    }
  }

  /**
   * 处理服务器响应
   */
  private handleServerResponse(data: ArrayBuffer): void {
    // 解析服务器响应并触发相应事件
    // 这里需要根据实际的协议格式解析
    console.log('收到服务器响应:', data.byteLength, '字节');

    // 触发服务器事件
    this.emit('serverEvent', {
      eventId: 451, // 示例事件ID
      data: data
    });
  }

  /**
   * 发送二进制消息
   */
  private sendBinary(data: Buffer): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，消息已加入队列');
      this.messageQueue.push(data);
      return;
    }

    this.ws.send(data);
  }

  /**
   * 断开连接（通过HTTP代理）
   */
  async disconnect(): Promise<void> {
    if (this.currentSessionId) {
      await this.endSession();
    }

    try {
      // 通知后端断开连接
      await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'disconnect',
          clientId: this.currentSessionId
        })
      });
    } catch (error) {
      console.error('断开连接失败:', error);
    }

    this.connectionState = ConnectionState.DISCONNECTED;
    this.emit('connectionStateChange', this.connectionState);
  }

  /**
   * 开始会话
   */
  async startSession(config: SessionConfig = {}): Promise<string> {
    if (this.connectionState !== ConnectionState.CONNECTED) {
      throw new Error('未连接到服务器');
    }

    if (this.sessionState !== SessionState.IDLE) {
      throw new Error('会话已存在');
    }

    this.sessionState = SessionState.STARTING;
    this.currentSessionId = this.generateSessionId();

    console.log('启动会话', {
      sessionId: this.currentSessionId,
      config
    });

    // 发送StartSession消息
    const sessionData = {
      dialog: {
        bot_name: config.botName || '豆包',
        dialog_id: config.dialogId || '',
        extra: {
          strict_audit: config.strictAudit !== false
        }
      },
      tts_config: config.ttsConfig || {
        audio_config: {
          channel: 1,
          format: 'pcm',
          sample_rate: 24000
        }
      }
    };

    const message = createClientEventMessage(
      ClientEventId.START_SESSION,
      this.currentSessionId,
      sessionData
    );

    this.sendBinary(message);

    this.sessionState = SessionState.ACTIVE;
    return this.currentSessionId;
  }



  /**
   * 发送音频数据（通过HTTP代理）
   */
  async sendAudio(audioData: ArrayBuffer): Promise<void> {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('发送音频数据', audioData.byteLength, '字节');

    try {
      // 创建音频消息
      const audioBuffer = Buffer.from(audioData);
      const message = createAudioMessage(audioBuffer, this.currentSessionId);

      // 转换为base64发送
      const base64Data = message.toString('base64');

      const response = await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send',
          clientId: this.currentSessionId,
          data: base64Data
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send audio data');
      }

      console.log('音频数据发送成功');
    } catch (error) {
      console.error('发送音频数据失败:', error);
      throw error;
    }
  }

  /**
   * 结束会话（通过HTTP代理）
   */
  async endSession(): Promise<void> {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      return;
    }

    this.sessionState = SessionState.ENDING;
    console.log('结束会话', this.currentSessionId);

    try {
      // 发送FinishSession消息
      const message = createClientEventMessage(
        ClientEventId.FINISH_SESSION,
        this.currentSessionId,
        {}
      );

      const base64Data = message.toString('base64');

      await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send',
          clientId: this.currentSessionId,
          data: base64Data
        })
      });
    } catch (error) {
      console.error('结束会话失败:', error);
    }

    this.currentSessionId = null;
    this.sessionState = SessionState.IDLE;
  }

  /**
   * 发送TTS文本
   */
  sendTTSText(content: string, start: boolean = false, end: boolean = false): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('发送TTS文本', { content, start, end });

    const message = createClientEventMessage(
      ClientEventId.CHAT_TTS_TEXT,
      this.currentSessionId,
      { start, content, end }
    );
    this.sendBinary(message);
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  // 事件系统
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }

  // Getters
  get connection(): ConnectionState {
    return this.connectionState;
  }

  get session(): SessionState {
    return this.sessionState;
  }
}
