/**
 * 豆包实时语音大模型API客户端 - 生产版本
 */

import {
  createClientEventMessage,
  createAudioMessage,
  ClientEventId,
  ServerEventId
} from './protocol';

// 连接配置
export interface DoubaoConfig {
  resourceId?: string;
  connectId?: string;
}

// 会话配置
export interface SessionConfig {
  botName?: string;
  dialogId?: string;
  strictAudit?: boolean;
  ttsConfig?: {
    audioConfig?: {
      channel: number;
      format: 'pcm' | 'ogg';
      sampleRate: number;
    };
  };
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 会话状态
export enum SessionState {
  IDLE = 'idle',
  STARTING = 'starting',
  ACTIVE = 'active',
  ENDING = 'ending',
  ERROR = 'error'
}

/**
 * 豆包实时语音客户端 - 生产版本
 */
export class DoubaoRealtimeClient {
  private config: DoubaoConfig;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private sessionState: SessionState = SessionState.IDLE;
  private currentSessionId: string | null = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private messageQueue: Buffer[] = [];
  private isProcessingMessages = false;

  constructor(config: DoubaoConfig = {}) {
    this.config = {
      resourceId: 'volc.speech.dialog',
      ...config
    };
  }

  /**
   * 连接到豆包API（通过HTTP代理）
   */
  async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED) {
      return;
    }

    this.connectionState = ConnectionState.CONNECTING;
    this.emit('connectionStateChange', this.connectionState);

    try {
      // 生成客户端ID
      this.currentSessionId = this.generateSessionId();

      // 通过HTTP API建立到豆包API的连接
      const response = await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'connect',
          clientId: this.currentSessionId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to connect to Doubao API');
      }

      const result = await response.json();
      console.log('豆包API连接成功:', result);

      // 启动消息监听
      this.startMessageListener();

      this.connectionState = ConnectionState.CONNECTED;
      this.emit('connectionStateChange', this.connectionState);

    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChange', this.connectionState);
      throw error;
    }
  }

  /**
   * 启动消息监听器（Server-Sent Events）
   */
  private startMessageListener(): void {
    if (!this.currentSessionId) return;

    // 创建Server-Sent Events连接
    fetch('/api/realtime/ws', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'listen',
        clientId: this.currentSessionId
      })
    }).then(response => {
      if (!response.ok) {
        throw new Error('Failed to start message listener');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader');
      }

      const decoder = new TextDecoder();

      const readMessages = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));
                  this.handleServerMessage(data);
                } catch (error) {
                  console.error('解析SSE消息失败:', error);
                }
              }
            }
          }
        } catch (error) {
          console.error('读取SSE消息失败:', error);
          // 重连逻辑
          if (this.connectionState === ConnectionState.CONNECTED) {
            setTimeout(() => this.startMessageListener(), 1000);
          }
        }
      };

      readMessages();
    }).catch(error => {
      console.error('启动消息监听器失败:', error);
    });
  }

  /**
   * 处理服务器消息
   */
  private handleServerMessage(message: any): void {
    console.log('收到服务器消息:', message);

    switch (message.type) {
      case 'connection_ready':
        console.log('消息监听器已就绪');
        break;

      case 'server_event':
        this.handleServerEvent(message);
        break;

      case 'audio_data':
        this.handleAudioData(message);
        break;

      case 'error':
        this.handleError(message);
        break;

      default:
        console.log('未知消息类型:', message.type);
    }
  }

  /**
   * 处理服务器事件
   */
  private handleServerEvent(message: any): void {
    const { eventId, data } = message;

    switch (eventId) {
      case 150: // SESSION_STARTED
        console.log('会话已启动:', data);
        this.emit('sessionStarted', data);
        break;

      case 451: // ASR_RESPONSE
        console.log('语音识别结果:', data);
        this.emit('asrResult', data);
        break;

      case 550: // CHAT_RESPONSE
        console.log('聊天响应:', data);
        this.emit('chatResponse', data);
        break;

      case 350: // TTS_SENTENCE_START
        console.log('TTS开始:', data);
        this.emit('ttsStart', data);
        break;

      case 351: // TTS_SENTENCE_END
        console.log('TTS结束:', data);
        this.emit('ttsEnd', data);
        break;

      default:
        console.log('未处理的服务器事件:', eventId, data);
        this.emit('serverEvent', { eventId, data });
    }
  }

  /**
   * 处理音频数据
   */
  private handleAudioData(message: any): void {
    console.log('收到音频数据:', message.data.length, '字节');
    this.emit('audioData', message.data);
  }

  /**
   * 处理错误
   */
  private handleError(message: any): void {
    console.error('服务器错误:', message);
    this.emit('error', new Error(message.data?.error || '未知错误'));
  }

  /**
   * 发送二进制消息到后端代理
   */
  private async sendBinary(data: Buffer): Promise<void> {
    if (!this.currentSessionId || this.connectionState !== ConnectionState.CONNECTED) {
      console.warn('连接未就绪，消息已加入队列');
      this.messageQueue.push(data);
      return;
    }

    try {
      // 转换为base64发送到后端
      const base64Data = data.toString('base64');

      const response = await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send',
          clientId: this.currentSessionId,
          data: base64Data
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send binary message');
      }

      console.log('二进制消息发送成功:', data.length, '字节');
    } catch (error) {
      console.error('发送二进制消息失败:', error);
      throw error;
    }
  }

  /**
   * 处理消息队列
   */
  private async processMessageQueue(): Promise<void> {
    if (this.isProcessingMessages || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessingMessages = true;

    try {
      while (this.messageQueue.length > 0) {
        const message = this.messageQueue.shift();
        if (message) {
          await this.sendBinary(message);
        }
      }
    } catch (error) {
      console.error('处理消息队列失败:', error);
    } finally {
      this.isProcessingMessages = false;
    }
  }

  /**
   * 断开连接（通过HTTP代理）
   */
  async disconnect(): Promise<void> {
    if (this.currentSessionId) {
      await this.endSession();
    }

    try {
      // 通知后端断开连接
      await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'disconnect',
          clientId: this.currentSessionId
        })
      });
    } catch (error) {
      console.error('断开连接失败:', error);
    }

    this.connectionState = ConnectionState.DISCONNECTED;
    this.emit('connectionStateChange', this.connectionState);
  }

  /**
   * 开始会话
   */
  async startSession(config: SessionConfig = {}): Promise<string> {
    if (this.connectionState !== ConnectionState.CONNECTED) {
      throw new Error('未连接到服务器');
    }

    if (this.sessionState !== SessionState.IDLE) {
      throw new Error('会话已存在');
    }

    this.sessionState = SessionState.STARTING;
    if (!this.currentSessionId) {
      this.currentSessionId = this.generateSessionId();
    }

    console.log('启动会话', {
      sessionId: this.currentSessionId,
      config
    });

    // 发送StartSession消息
    const sessionData = {
      dialog: {
        bot_name: config.botName || '豆包',
        dialog_id: config.dialogId || '',
        extra: {
          strict_audit: config.strictAudit !== false
        }
      },
      tts: config.ttsConfig || {
        audio_config: {
          channel: 1,
          format: 'pcm',
          sample_rate: 24000
        }
      }
    };

    const message = createClientEventMessage(
      ClientEventId.START_SESSION,
      this.currentSessionId,
      sessionData
    );

    await this.sendBinary(message);

    // 处理消息队列
    await this.processMessageQueue();

    this.sessionState = SessionState.ACTIVE;
    return this.currentSessionId;
  }



  /**
   * 发送音频数据（通过HTTP代理）
   */
  async sendAudio(audioData: ArrayBuffer): Promise<void> {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('发送音频数据', audioData.byteLength, '字节');

    try {
      // 创建音频消息
      const audioBuffer = Buffer.from(audioData);
      const message = createAudioMessage(audioBuffer, this.currentSessionId);

      // 转换为base64发送
      const base64Data = message.toString('base64');

      const response = await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send',
          clientId: this.currentSessionId,
          data: base64Data
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send audio data');
      }

      console.log('音频数据发送成功');
    } catch (error) {
      console.error('发送音频数据失败:', error);
      throw error;
    }
  }

  /**
   * 结束会话（通过HTTP代理）
   */
  async endSession(): Promise<void> {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      return;
    }

    this.sessionState = SessionState.ENDING;
    console.log('结束会话', this.currentSessionId);

    try {
      // 发送FinishSession消息
      const message = createClientEventMessage(
        ClientEventId.FINISH_SESSION,
        this.currentSessionId,
        {}
      );

      const base64Data = message.toString('base64');

      await fetch('/api/realtime/ws', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'send',
          clientId: this.currentSessionId,
          data: base64Data
        })
      });
    } catch (error) {
      console.error('结束会话失败:', error);
    }

    this.currentSessionId = null;
    this.sessionState = SessionState.IDLE;
  }

  /**
   * 发送TTS文本
   */
  async sendTTSText(content: string, start: boolean = false, end: boolean = false): Promise<void> {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('发送TTS文本', { content, start, end });

    const message = createClientEventMessage(
      ClientEventId.CHAT_TTS_TEXT,
      this.currentSessionId,
      { start, content, end }
    );

    await this.sendBinary(message);
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  // 事件系统
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }

  // Getters
  get connection(): ConnectionState {
    return this.connectionState;
  }

  get session(): SessionState {
    return this.sessionState;
  }
}
