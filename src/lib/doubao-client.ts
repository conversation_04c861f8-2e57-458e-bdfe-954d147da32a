/**
 * 豆包实时语音大模型API客户端 - WebSocket直连版本
 */

import {
  createClientEventMessage,
  createAudioMessage,
  ClientEventId,
  ServerEventId,
  decodeHeader,
  MessageType
} from './protocol';

// 连接配置
export interface DoubaoConfig {
  resourceId?: string;
  connectId?: string;
}

// 会话配置
export interface SessionConfig {
  botName?: string;
  dialogId?: string;
  strictAudit?: boolean;
  ttsConfig?: {
    audioConfig?: {
      channel: number;
      format: 'pcm' | 'ogg';
      sampleRate: number;
    };
  };
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 会话状态
export enum SessionState {
  IDLE = 'idle',
  STARTING = 'starting',
  ACTIVE = 'active',
  ENDING = 'ending',
  ERROR = 'error'
}

/**
 * 豆包实时语音客户端 - 生产版本
 */
export class DoubaoRealtimeClient {
  private ws: WebSocket | null = null;
  private config: DoubaoConfig;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private sessionState: SessionState = SessionState.IDLE;
  private currentSessionId: string | null = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private messageQueue: Buffer[] = [];
  private isProcessingMessages = false;

  constructor(config: DoubaoConfig = {}) {
    this.config = {
      resourceId: 'volc.speech.dialog',
      ...config
    };
  }

  /**
   * 连接到豆包API（通过WebSocket直连）
   */
  async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED) {
      return;
    }

    this.connectionState = ConnectionState.CONNECTING;
    this.emit('connectionStateChange', this.connectionState);

    try {
      // 生成客户端ID
      this.currentSessionId = this.generateSessionId();

      // 创建WebSocket连接到我们的Next.js服务器
      const wsUrl = `ws://localhost:3000/api/realtime/ws`;
      this.ws = new WebSocket(wsUrl);

      // 设置WebSocket事件处理器
      this.setupWebSocketHandlers();

      // 等待连接建立
      await this.waitForConnection();

      this.connectionState = ConnectionState.CONNECTED;
      this.emit('connectionStateChange', this.connectionState);

      // 处理消息队列
      this.processMessageQueue();

    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChange', this.connectionState);
      throw error;
    }
  }

  /**
   * 设置WebSocket事件处理器
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(event.data);
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket连接已关闭', event.code, event.reason);
      this.connectionState = ConnectionState.DISCONNECTED;
      this.emit('connectionStateChange', this.connectionState);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChange', this.connectionState);
      this.emit('error', error);
    };
  }

  /**
   * 等待WebSocket连接建立
   */
  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.ws) {
        reject(new Error('WebSocket未初始化'));
        return;
      }

      if (this.ws.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('连接超时'));
      }, 10000);

      this.ws.onopen = () => {
        clearTimeout(timeout);
        resolve();
      };

      this.ws.onerror = () => {
        clearTimeout(timeout);
        reject(new Error('连接失败'));
      };
    });
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: any): void {
    try {
      // 检查是否为JSON消息（连接状态消息）
      if (typeof data === 'string') {
        try {
          const json = JSON.parse(data);
          if (json.type === 'connection' && json.status === 'connected') {
            console.log('WebSocket代理连接成功');
            return;
          }
          if (json.type === 'error') {
            console.error('WebSocket代理错误:', json.message);
            this.emit('error', new Error(json.message));
            return;
          }
        } catch {
          // 不是JSON，继续处理为二进制协议
        }
      }

      // 处理二进制协议消息
      if (data instanceof ArrayBuffer || data instanceof Blob) {
        this.handleBinaryMessage(data);
      }
    } catch (error) {
      console.error('处理消息失败:', error);
    }
  }

  /**
   * 处理二进制协议消息
   */
  private async handleBinaryMessage(data: ArrayBuffer | Blob): Promise<void> {
    try {
      let buffer: Buffer;

      if (data instanceof Blob) {
        const arrayBuffer = await data.arrayBuffer();
        buffer = Buffer.from(arrayBuffer);
      } else {
        buffer = Buffer.from(data);
      }

      // 解析二进制协议
      const header = decodeHeader(buffer);
      console.log('收到豆包API消息:', {
        messageType: header.messageType,
        serializationMethod: header.serializationMethod,
        compressionMethod: header.compressionMethod
      });

      // 根据消息类型处理
      if (header.messageType === MessageType.FULL_SERVER_RESPONSE) {
        this.handleServerResponse(buffer, header);
      } else if (header.messageType === MessageType.AUDIO_ONLY_RESPONSE) {
        this.handleAudioResponse(buffer, header);
      } else if (header.messageType === MessageType.ERROR_INFORMATION) {
        this.handleErrorResponse(buffer, header);
      }
    } catch (error) {
      console.error('处理二进制消息失败:', error);
    }
  }

  /**
   * 处理服务器响应
   */
  private handleServerResponse(buffer: Buffer, header: any): void {
    try {
      // 跳过协议头(4字节)
      let offset = 4;

      // 解析可选字段
      const eventId = buffer.readUInt32LE(offset);
      offset += 4;

      // 解析session ID长度和内容
      const sessionIdSize = buffer.readUInt32LE(offset);
      offset += 4;
      const sessionId = buffer.subarray(offset, offset + sessionIdSize).toString('utf8');
      offset += sessionIdSize;

      // 解析payload长度
      const payloadSize = buffer.readUInt32LE(offset);
      offset += 4;

      // 解析payload
      let payload = buffer.subarray(offset, offset + payloadSize);

      // 如果使用了压缩，需要解压
      if (header.compressionMethod === 1) { // GZIP
        const zlib = require('zlib');
        payload = zlib.gunzipSync(payload);
      }

      // 解析JSON
      const jsonData = JSON.parse(payload.toString('utf8'));

      // 触发相应事件
      this.handleServerEvent({ eventId, sessionId, data: jsonData });
    } catch (error) {
      console.error('解析服务器响应失败:', error);
    }
  }

  /**
   * 处理音频响应
   */
  private handleAudioResponse(buffer: Buffer, header: any): void {
    try {
      // 跳过协议头和可选字段，直接获取音频数据
      let offset = 4;

      // 跳过事件ID
      offset += 4;

      // 跳过session ID
      const sessionIdSize = buffer.readUInt32LE(offset);
      offset += 4 + sessionIdSize;

      // 获取音频数据长度
      const payloadSize = buffer.readUInt32LE(offset);
      offset += 4;

      // 获取音频数据
      const audioData = buffer.subarray(offset, offset + payloadSize);

      console.log('收到音频数据:', audioData.length, '字节');
      this.emit('audioData', audioData);
    } catch (error) {
      console.error('解析音频响应失败:', error);
    }
  }

  /**
   * 处理错误响应
   */
  private handleErrorResponse(buffer: Buffer, header: any): void {
    try {
      // 类似于服务器响应的解析
      let offset = 4;

      // 错误码
      const errorCode = buffer.readUInt32LE(offset);
      offset += 4;

      // payload
      const payloadSize = buffer.readUInt32LE(offset);
      offset += 4;
      const payload = buffer.subarray(offset, offset + payloadSize);

      const errorData = JSON.parse(payload.toString('utf8'));

      console.error('收到错误响应:', { errorCode, data: errorData });
      this.emit('error', new Error(errorData.error || '未知错误'));
    } catch (error) {
      console.error('解析错误响应失败:', error);
    }
  }

  /**
   * 处理服务器事件
   */
  private handleServerEvent(message: any): void {
    const { eventId, data } = message;

    switch (eventId) {
      case 150: // SESSION_STARTED
        console.log('会话已启动:', data);
        this.emit('sessionStarted', data);
        break;

      case 451: // ASR_RESPONSE
        console.log('语音识别结果:', data);
        this.emit('asrResult', data);
        break;

      case 550: // CHAT_RESPONSE
        console.log('聊天响应:', data);
        this.emit('chatResponse', data);
        break;

      case 350: // TTS_SENTENCE_START
        console.log('TTS开始:', data);
        this.emit('ttsStart', data);
        break;

      case 351: // TTS_SENTENCE_END
        console.log('TTS结束:', data);
        this.emit('ttsEnd', data);
        break;

      default:
        console.log('未处理的服务器事件:', eventId, data);
        this.emit('serverEvent', { eventId, data });
    }
  }

  /**
   * 处理音频数据
   */
  private handleAudioData(message: any): void {
    console.log('收到音频数据:', message.data.length, '字节');
    this.emit('audioData', message.data);
  }

  /**
   * 处理错误
   */
  private handleError(message: any): void {
    console.error('服务器错误:', message);
    this.emit('error', new Error(message.data?.error || '未知错误'));
  }

  /**
   * 发送二进制消息
   */
  private sendBinary(data: Buffer): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，消息已加入队列');
      this.messageQueue.push(data);
      return;
    }

    try {
      this.ws.send(data);
      console.log('二进制消息发送成功:', data.length, '字节');
    } catch (error) {
      console.error('发送二进制消息失败:', error);
      throw error;
    }
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    if (this.isProcessingMessages || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessingMessages = true;

    try {
      while (this.messageQueue.length > 0) {
        const message = this.messageQueue.shift();
        if (message) {
          this.sendBinary(message);
        }
      }
    } catch (error) {
      console.error('处理消息队列失败:', error);
    } finally {
      this.isProcessingMessages = false;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.currentSessionId) {
      await this.endSession();
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.connectionState = ConnectionState.DISCONNECTED;
    this.emit('connectionStateChange', this.connectionState);
  }

  /**
   * 开始会话
   */
  async startSession(config: SessionConfig = {}): Promise<string> {
    if (this.connectionState !== ConnectionState.CONNECTED) {
      throw new Error('未连接到服务器');
    }

    if (this.sessionState !== SessionState.IDLE) {
      throw new Error('会话已存在');
    }

    this.sessionState = SessionState.STARTING;
    if (!this.currentSessionId) {
      this.currentSessionId = this.generateSessionId();
    }

    console.log('启动会话', {
      sessionId: this.currentSessionId,
      config
    });

    // 发送StartSession消息
    const sessionData = {
      dialog: {
        bot_name: config.botName || '豆包',
        dialog_id: config.dialogId || '',
        extra: {
          strict_audit: config.strictAudit !== false
        }
      },
      tts: config.ttsConfig || {
        audio_config: {
          channel: 1,
          format: 'pcm',
          sample_rate: 24000
        }
      }
    };

    const message = createClientEventMessage(
      ClientEventId.START_SESSION,
      this.currentSessionId,
      sessionData
    );

    this.sendBinary(message);

    // 处理消息队列
    this.processMessageQueue();

    this.sessionState = SessionState.ACTIVE;
    return this.currentSessionId;
  }



  /**
   * 发送音频数据
   */
  sendAudio(audioData: ArrayBuffer): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('发送音频数据', audioData.byteLength, '字节');

    try {
      // 创建音频消息
      const audioBuffer = Buffer.from(audioData);
      const message = createAudioMessage(audioBuffer, this.currentSessionId);

      this.sendBinary(message);
      console.log('音频数据发送成功');
    } catch (error) {
      console.error('发送音频数据失败:', error);
      throw error;
    }
  }

  /**
   * 结束会话
   */
  async endSession(): Promise<void> {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      return;
    }

    this.sessionState = SessionState.ENDING;
    console.log('结束会话', this.currentSessionId);

    try {
      // 发送FinishSession消息
      const message = createClientEventMessage(
        ClientEventId.FINISH_SESSION,
        this.currentSessionId,
        {}
      );

      this.sendBinary(message);
    } catch (error) {
      console.error('结束会话失败:', error);
    }

    this.currentSessionId = null;
    this.sessionState = SessionState.IDLE;
  }

  /**
   * 发送TTS文本
   */
  sendTTSText(content: string, start: boolean = false, end: boolean = false): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('发送TTS文本', { content, start, end });

    const message = createClientEventMessage(
      ClientEventId.CHAT_TTS_TEXT,
      this.currentSessionId,
      { start, content, end }
    );

    this.sendBinary(message);
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  // 事件系统
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }

  // Getters
  get connection(): ConnectionState {
    return this.connectionState;
  }

  get session(): SessionState {
    return this.sessionState;
  }
}
