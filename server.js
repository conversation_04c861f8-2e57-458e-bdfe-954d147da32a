/**
 * 自定义Next.js服务器 - 支持WebSocket直连豆包API
 */

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const WebSocket = require('ws');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

// 豆包API配置
const DOUBAO_CONFIG = {
  appId: process.env.DOUBAO_APP_ID || '2919022726',
  accessToken: process.env.DOUBAO_ACCESS_TOKEN || 'EDQbT-FiT72TPSeOMnePDef1OFKYCAp5',
  resourceId: process.env.DOUBAO_RESOURCE_ID || 'volc.speech.dialog',
  appKey: 'PlgvMymc7f3tQnJ6'
};

// 存储客户端连接映射
const clientConnections = new Map();

// 创建Next.js应用
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

/**
 * 创建到豆包API的WebSocket连接
 */
function createDoubaoConnection(clientId, clientWs) {
  console.log(`为客户端 ${clientId} 创建豆包API连接`);

  const doubaoWs = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue', {
    headers: {
      'X-Api-App-ID': DOUBAO_CONFIG.appId,
      'X-Api-Access-Key': DOUBAO_CONFIG.accessToken,
      'X-Api-Resource-Id': DOUBAO_CONFIG.resourceId,
      'X-Api-App-Key': DOUBAO_CONFIG.appKey,
      'X-Api-Connect-Id': `connect-${clientId}`,
      'User-Agent': 'DoubaoRealtimeClient/1.0'
    }
  });

  // 保存连接映射
  clientConnections.set(clientId, { clientWs, doubaoWs });

  // 豆包WebSocket事件处理
  doubaoWs.on('open', () => {
    console.log(`豆包API连接已建立 - 客户端: ${clientId}`);
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.send(JSON.stringify({
        type: 'connection',
        status: 'connected',
        clientId
      }));
    }
  });

  doubaoWs.on('message', (data) => {
    console.log(`收到豆包API消息 - 客户端: ${clientId}, 数据长度: ${data.length}`);
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.send(data);
    }
  });

  doubaoWs.on('error', (error) => {
    console.error(`豆包API连接错误 - 客户端: ${clientId}`, error);
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.send(JSON.stringify({
        type: 'error',
        message: error.message,
        clientId
      }));
    }
  });

  doubaoWs.on('close', (code, reason) => {
    console.log(`豆包API连接关闭 - 客户端: ${clientId}`, code, reason);
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.close(code, reason);
    }
    clientConnections.delete(clientId);
  });

  // 客户端WebSocket事件处理
  clientWs.on('message', (data) => {
    console.log(`收到客户端消息 - 客户端: ${clientId}, 数据长度: ${data.length}`);
    if (doubaoWs.readyState === WebSocket.OPEN) {
      doubaoWs.send(data);
    }
  });

  clientWs.on('error', (error) => {
    console.error(`客户端WebSocket错误 - 客户端: ${clientId}`, error);
    if (doubaoWs.readyState === WebSocket.OPEN) {
      doubaoWs.close();
    }
  });

  clientWs.on('close', (code, reason) => {
    console.log(`客户端WebSocket连接关闭 - 客户端: ${clientId}`, code, reason);
    if (doubaoWs.readyState === WebSocket.OPEN) {
      doubaoWs.close();
    }
    clientConnections.delete(clientId);
  });
}

app.prepare().then(() => {
  // 创建HTTP服务器
  const server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // 创建WebSocket服务器
  const wss = new WebSocket.Server({
    server,
    path: '/api/realtime/ws'
  });

  console.log('> WebSocket服务器已启动，路径: /api/realtime/ws');

  // 处理WebSocket连接
  wss.on('connection', (clientWs, request) => {
    const clientId = `client-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    console.log(`客户端连接: ${clientId}`);

    // 创建到豆包API的连接
    createDoubaoConnection(clientId, clientWs);
  });

  // 启动服务器
  server.listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://${hostname}:${port}`);
    console.log('> WebSocket服务器已启用');
    console.log(`> 活跃连接数: ${clientConnections.size}`);
  });

  // 定时清理超时连接
  setInterval(() => {
    console.log(`> 当前活跃连接数: ${clientConnections.size}`);
  }, 60000); // 每分钟输出一次状态

  // 优雅关闭
  const shutdown = () => {
    console.log('正在关闭服务器...');

    // 关闭所有WebSocket连接
    clientConnections.forEach(({ clientWs, doubaoWs }, clientId) => {
      console.log(`关闭连接: ${clientId}`);
      if (clientWs.readyState === WebSocket.OPEN) {
        clientWs.close();
      }
      if (doubaoWs.readyState === WebSocket.OPEN) {
        doubaoWs.close();
      }
    });

    clientConnections.clear();

    // 关闭WebSocket服务器
    wss.close(() => {
      console.log('WebSocket服务器已关闭');
    });

    // 关闭HTTP服务器
    server.close(() => {
      console.log('HTTP服务器已关闭');
      process.exit(0);
    });
  };

  process.on('SIGTERM', shutdown);
  process.on('SIGINT', shutdown);
});
