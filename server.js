/**
 * 自定义Next.js服务器 - 支持WebSocket代理
 */

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

// 创建Next.js应用
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  // 创建HTTP服务器
  const server = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // 启动WebSocket代理
  try {
    const { wsProxy } = require('./src/lib/websocket-proxy.js');
    wsProxy.start(server);
  } catch (error) {
    console.log('WebSocket代理暂时不可用，使用基础模式:', error.message);
  }

  // 启动服务器
  server.listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://${hostname}:${port}`);
    console.log('> WebSocket proxy enabled');
  });

  // 优雅关闭
  process.on('SIGTERM', () => {
    console.log('Received SIGTERM, shutting down gracefully');
    wsProxy.stop();
    server.close(() => {
      console.log('Server closed');
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    console.log('Received SIGINT, shutting down gracefully');
    wsProxy.stop();
    server.close(() => {
      console.log('Server closed');
      process.exit(0);
    });
  });
});
