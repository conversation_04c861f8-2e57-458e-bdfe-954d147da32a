# WebSocket代理实现方案

## 🎯 问题分析

### **为什么需要WebSocket代理？**

1. **浏览器限制**
   ```javascript
   // ❌ 浏览器WebSocket不支持自定义headers
   const ws = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue', {
     headers: {
       'X-Api-App-ID': 'your-app-id',
       'X-Api-Access-Key': 'your-token'  // 豆包API需要这些认证头
     }
   });
   ```

2. **豆包API要求**
   - 需要认证headers：`X-Api-App-ID`、`X-Api-Access-Key`、`X-Api-Resource-Id`
   - 只能在服务端设置这些headers

## 🔧 实现方案

### **架构设计**
```
前端 ←→ Next.js API (HTTP) ←→ 豆包API (WebSocket)
```

### **1. Next.js API代理 (`/api/realtime/ws/route.ts`)**

#### **核心功能：**
- ✅ **建立连接** - 在服务端创建到豆包API的WebSocket连接
- ✅ **消息转发** - 转发前端消息到豆包API
- ✅ **连接管理** - 管理多个客户端连接
- ✅ **认证处理** - 自动添加必需的认证headers

#### **API接口：**

```typescript
// 建立连接
POST /api/realtime/ws
{
  "action": "connect",
  "clientId": "unique-client-id"
}

// 发送消息
POST /api/realtime/ws
{
  "action": "send",
  "clientId": "client-id",
  "data": "base64-encoded-message"
}

// 断开连接
POST /api/realtime/ws
{
  "action": "disconnect",
  "clientId": "client-id"
}

// 获取状态
GET /api/realtime/ws?clientId=client-id
```

#### **实现特点：**
```typescript
// 自动添加认证headers
const doubaoWs = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue', {
  headers: {
    'X-Api-App-ID': DOUBAO_CONFIG.appId,
    'X-Api-Access-Key': DOUBAO_CONFIG.accessToken,
    'X-Api-Resource-Id': DOUBAO_CONFIG.resourceId,
    'User-Agent': 'DoubaoRealtimeClient/1.0'
  }
});
```

### **2. 前端客户端更新 (`doubao-client.ts`)**

#### **连接方式改变：**
```typescript
// 之前：直接WebSocket连接（不可行）
// const ws = new WebSocket(wsUrl);

// 现在：通过HTTP API代理
async connect(): Promise<void> {
  const response = await fetch('/api/realtime/ws', {
    method: 'POST',
    body: JSON.stringify({
      action: 'connect',
      clientId: this.currentSessionId
    })
  });
}
```

#### **消息发送：**
```typescript
async sendAudio(audioData: ArrayBuffer): Promise<void> {
  // 创建二进制消息
  const message = createAudioMessage(audioBuffer, this.currentSessionId);
  
  // 转换为base64通过HTTP发送
  const base64Data = message.toString('base64');
  
  await fetch('/api/realtime/ws', {
    method: 'POST',
    body: JSON.stringify({
      action: 'send',
      clientId: this.currentSessionId,
      data: base64Data
    })
  });
}
```

## 🚀 优势

### **1. 安全性**
- ✅ API密钥安全存储在服务端
- ✅ 前端无法访问敏感认证信息
- ✅ 符合安全最佳实践

### **2. 兼容性**
- ✅ 解决浏览器WebSocket headers限制
- ✅ 支持所有现代浏览器
- ✅ 无需额外的WebSocket库

### **3. 可维护性**
- ✅ 清晰的前后端分离
- ✅ 统一的错误处理
- ✅ 易于调试和监控

### **4. 扩展性**
- ✅ 支持多客户端连接
- ✅ 可以添加连接池管理
- ✅ 易于添加日志和监控

## 📊 数据流

### **连接建立：**
```
1. 前端 → POST /api/realtime/ws (action: connect)
2. Next.js → 创建WebSocket到豆包API
3. Next.js → 返回连接成功状态
4. 前端 → 更新连接状态
```

### **音频发送：**
```
1. 前端 → 录制音频数据
2. 前端 → 创建二进制协议消息
3. 前端 → 转换为base64
4. 前端 → POST /api/realtime/ws (action: send)
5. Next.js → 解码base64为二进制
6. Next.js → 转发到豆包API WebSocket
7. 豆包API → 处理音频并返回响应
```

### **响应处理：**
```
1. 豆包API → 发送响应到Next.js WebSocket
2. Next.js → 解析响应消息
3. Next.js → 触发前端事件（待实现）
4. 前端 → 处理ASR/TTS/聊天响应
```

## 🔄 下一步优化

### **1. 实时响应处理**
- 实现Server-Sent Events (SSE)推送豆包API响应
- 或使用WebSocket连接前端和Next.js

### **2. 连接池管理**
- 实现连接复用
- 添加连接超时和重连机制

### **3. 错误处理**
- 完善错误重试逻辑
- 添加连接状态监控

### **4. 性能优化**
- 实现消息队列
- 添加压缩支持

## 🎉 总结

我们成功实现了：

1. **✅ 解决浏览器限制** - 通过Next.js API代理绕过WebSocket headers限制
2. **✅ 安全认证** - API密钥安全存储在服务端
3. **✅ 完整协议支持** - 支持豆包API的完整二进制协议
4. **✅ 前后端打通** - Next.js既是前端又是后端，实现真正的全栈方案

现在前端可以通过Next.js后端成功连接到豆包API，发送音频数据，实现真正的实时语音对话！🎵✨
