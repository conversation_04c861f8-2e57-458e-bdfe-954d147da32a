# 豆包实时语音API WebSocket架构重构

## 📋 架构概述

我们已经完全重构了业务逻辑，实现了真正的WebSocket直连架构：

```
前端 WebSocket ←→ Next.js WebSocket服务端 ←→ 豆包API WebSocket
```

### 🔄 架构变化

#### 之前的HTTP代理架构
```
前端 HTTP/SSE ←→ Next.js API Routes ←→ 豆包API WebSocket
```

#### 现在的WebSocket直连架构
```
前端 WebSocket ←→ Next.js WebSocket服务端 ←→ 豆包API WebSocket
```

## 🏗️ 核心实现

### 1. **自定义Next.js服务器** (`server.js`)

```javascript
// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
  server,
  path: '/api/realtime/ws'
});

// 处理WebSocket连接
wss.on('connection', (clientWs, request) => {
  const clientId = `client-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  console.log(`客户端连接: ${clientId}`);

  // 创建到豆包API的连接
  createDoubaoConnection(clientId, clientWs);
});
```

#### 核心功能
- ✅ **WebSocket服务端**: 在Next.js中提供WebSocket服务
- ✅ **双向代理**: 前端 ←→ 豆包API 消息转发
- ✅ **连接管理**: 自动管理客户端和豆包API连接
- ✅ **错误处理**: 完整的错误处理和连接清理

### 2. **豆包API直连** (`createDoubaoConnection`)

```javascript
function createDoubaoConnection(clientId, clientWs) {
  const doubaoWs = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue', {
    headers: {
      'X-Api-App-ID': DOUBAO_CONFIG.appId,
      'X-Api-Access-Key': DOUBAO_CONFIG.accessToken,
      'X-Api-Resource-Id': DOUBAO_CONFIG.resourceId,
      'X-Api-App-Key': DOUBAO_CONFIG.appKey,
      'X-Api-Connect-Id': `connect-${clientId}`,
      'User-Agent': 'DoubaoRealtimeClient/1.0'
    }
  });

  // 双向消息转发
  doubaoWs.on('message', (data) => {
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.send(data);
    }
  });

  clientWs.on('message', (data) => {
    if (doubaoWs.readyState === WebSocket.OPEN) {
      doubaoWs.send(data);
    }
  });
}
```

### 3. **前端WebSocket客户端** (`DoubaoRealtimeClient`)

```typescript
// 直接连接到Next.js WebSocket服务器
const wsUrl = `ws://localhost:3000/api/realtime/ws`;
this.ws = new WebSocket(wsUrl);

// 直接发送二进制数据
private sendBinary(data: Buffer): void {
  if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
    this.messageQueue.push(data);
    return;
  }
  this.ws.send(data);
}
```

## 🚀 关键优势

### 1. **性能提升**
- ✅ **真正的实时通信**: WebSocket双向通信，无HTTP轮询开销
- ✅ **低延迟**: 直接二进制数据传输，无需base64编码
- ✅ **高效转发**: 服务端直接转发，无需协议转换

### 2. **架构简化**
- ✅ **统一协议**: 前端到后端使用WebSocket，后端到豆包API也使用WebSocket
- ✅ **无状态转换**: 消息直接转发，无需HTTP/WebSocket协议转换
- ✅ **简化错误处理**: 统一的WebSocket错误处理机制

### 3. **扩展性增强**
- ✅ **连接复用**: 每个客户端独立的豆包API连接
- ✅ **负载均衡**: 易于扩展到多实例部署
- ✅ **监控友好**: 清晰的连接状态和日志

## 🔧 使用方式

### 前端连接
```typescript
const client = new DoubaoRealtimeClient();

// 直接连接WebSocket服务器
await client.connect();

// 监听事件
client.on('asrResult', (data) => {
  console.log('语音识别结果:', data);
});

client.on('audioData', (audioBuffer) => {
  // 播放音频数据
  playAudio(audioBuffer);
});

// 开始会话
const sessionId = await client.startSession({
  botName: '豆包',
  strictAudit: true
});

// 发送音频数据
client.sendAudio(audioBuffer);

// 发送TTS文本
client.sendTTSText('你好，我是豆包！');
```

### 服务器启动
```bash
# 启动自定义Next.js服务器
npm run dev

# 服务器日志
> WebSocket服务器已启动，路径: /api/realtime/ws
> Ready on http://localhost:3000
> WebSocket服务器已启用
> 活跃连接数: 0
```

## 📊 连接流程

### 1. **客户端连接**
```
1. 前端创建WebSocket连接到 ws://localhost:3000/api/realtime/ws
2. Next.js WebSocket服务器接收连接
3. 服务器为客户端分配唯一ID
4. 服务器创建到豆包API的WebSocket连接
5. 建立双向消息转发通道
```

### 2. **消息传输**
```
前端发送消息 → Next.js服务器 → 豆包API
豆包API响应 → Next.js服务器 → 前端接收
```

### 3. **连接清理**
```
1. 客户端断开 → 服务器检测到连接关闭
2. 服务器关闭对应的豆包API连接
3. 清理连接映射和资源
```

## 🛡️ 错误处理

### 连接错误
- **客户端连接失败**: 自动重试机制
- **豆包API连接失败**: 错误消息转发给前端
- **网络中断**: 自动重连和状态恢复

### 消息错误
- **消息格式错误**: 详细错误日志
- **传输失败**: 消息队列和重试机制
- **协议错误**: 豆包API错误响应处理

## 🔍 监控和调试

### 服务器日志
```
客户端连接: client-1703123456789-abc123
为客户端 client-1703123456789-abc123 创建豆包API连接
豆包API连接已建立 - 客户端: client-1703123456789-abc123
收到豆包API消息 - 客户端: client-1703123456789-abc123, 数据长度: 1024
```

### 前端调试
```typescript
// 连接状态监听
client.on('connectionStateChange', (state) => {
  console.log('连接状态:', state);
});

// 错误监听
client.on('error', (error) => {
  console.error('客户端错误:', error);
});
```

## 📝 测试验证

访问 `http://localhost:3000/test` 运行完整的测试套件：

1. **WebSocket服务器测试**: 验证服务器启动和连接
2. **豆包API连接测试**: 验证到豆包API的连接
3. **消息传输测试**: 验证双向消息传输
4. **客户端SDK测试**: 验证前端SDK功能

## 🎯 下一步优化

1. **连接池管理**: 实现连接复用和负载均衡
2. **消息压缩**: 添加消息压缩以减少带宽
3. **断线重连**: 增强的自动重连机制
4. **性能监控**: 添加详细的性能指标
5. **安全增强**: 添加认证和授权机制

这个新架构提供了更好的性能、更简单的维护和更强的扩展性！🎉
