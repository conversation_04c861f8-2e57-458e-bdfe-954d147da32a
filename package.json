{"name": "doubao-realtime", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:next": "next dev", "build": "next build", "start": "NODE_ENV=production node server.js", "start:next": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/ws": "^8.18.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "ws": "^8.18.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}