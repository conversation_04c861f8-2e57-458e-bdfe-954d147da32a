# 豆包实时语音对话应用

基于豆包端到端实时语音大模型API构建的语音交互应用，支持实时语音对话功能。

## 功能特性

- 🎤 **实时语音对话** - 类似打电话的连续对话体验，无需手动开始/结束录音
- 🔊 **智能语音打断** - 检测到用户说话时自动打断AI播放，实现自然对话
- 🎯 **VAD语音检测** - 自动检测语音活动，智能识别说话开始和结束
- 📱 **双模式支持** - 支持实时对话模式和传统按键说话模式
- 🎨 **现代UI** - 使用shadcn/ui组件库，实时状态显示
- ⚡ **低延迟交互** - 优化的音频处理和流式播放
- 🛡️ **安全审核** - 支持严格审核模式配置

## 技术栈

- **前端框架**: Next.js 15 + React 19
- **UI组件**: shadcn/ui + Tailwind CSS
- **音频处理**: Web Audio API
- **实时通信**: WebSocket
- **语言**: TypeScript

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动开发服务器

```bash
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 3. 配置API密钥

API密钥已在后端配置，确保安全性。如需修改，请：

**方式一：环境变量配置（推荐）**
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑 .env.local 文件，填入你的API密钥
DOUBAO_APP_ID=你的App ID
DOUBAO_ACCESS_TOKEN=你的Access Token
```

**方式二：直接修改代码**
编辑 `src/app/api/realtime/route.ts` 文件中的 `DOUBAO_CONFIG` 配置

## 使用说明

### 基本流程

1. **启动应用** - 后端自动加载API配置，无需手动输入密钥
2. **连接服务** - 点击"连接"按钮建立与豆包API的连接
3. **开始会话** - 连接成功后点击"开始会话"
4. **选择对话模式**：
   - **实时对话模式** - 点击"开启实时对话"，像打电话一样自然对话
   - **按键说话模式** - 点击麦克风按钮录音，松开发送
5. **语音交互** - 开始说话，AI会实时响应并播放语音回复

### 功能特色

- 🔄 **实时对话模式** - 连续对话，自动检测语音，智能打断
- 🎤 **按键说话模式** - 传统录音模式，手动控制录音
- 📊 **实时状态显示** - 音量指示、说话状态、AI播放状态
- 👋 **快速打招呼** - 一键发送问候语
- ⚙️ **灵活配置** - 支持多种参数配置
- 🔌 **连接管理** - 实时连接状态监控

## API文档参考

本项目基于豆包端到端实时语音大模型API开发，详细API文档请参考：
[豆包实时语音API文档](https://www.volcengine.com/docs/6561/1594356)

## 注意事项

### 演示模式

当前版本运行在演示模式下，模拟了豆包API的响应。在生产环境中需要：

1. **服务端代理** - 由于浏览器WebSocket不支持自定义headers，需要通过服务端代理处理认证
2. **真实API集成** - 替换演示模式的模拟响应为真实API调用
3. **错误处理** - 完善生产环境的错误处理和重试机制

### 浏览器兼容性

- 需要支持Web Audio API的现代浏览器
- 需要麦克风权限
- 建议使用Chrome、Firefox、Safari等主流浏览器

## 许可证

MIT License
