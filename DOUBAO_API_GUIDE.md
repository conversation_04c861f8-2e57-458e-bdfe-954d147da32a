# 豆包实时语音API使用指南

基于官方文档：https://www.volcengine.com/docs/6561/1594356

## 🎯 API概述

豆包端到端实时语音大模型API提供了完整的语音对话能力，包括：
- **实时语音识别（ASR）** - 将语音转换为文本
- **大模型对话（Chat）** - 智能对话生成
- **语音合成（TTS）** - 将文本转换为语音

## 🔧 技术规格

### 音频格式要求

#### 客户端上传（ASR输入）
- **格式**: PCM
- **声道**: 单声道
- **采样率**: 16000Hz
- **采样格式**: int16（16位整数）
- **字节序**: 小端序（Little Endian）

#### 服务端返回（TTS输出）
- **格式**: PCM 或 OGG封装的Opus
- **声道**: 单声道
- **采样率**: 24000Hz（PCM）/ 16000Hz（Opus）
- **采样格式**: Float32（PCM）/ Opus编码（OGG）
- **字节序**: 小端序

### WebSocket连接

#### 连接地址
```
wss://openspeech.bytedance.com/api/v3/realtime/dialogue
```

#### 认证头部
```
X-Api-App-ID: 你的App ID
X-Api-Access-Key: 你的Access Token
X-Api-Resource-Id: volc.speech.dialog
```

## 📡 协议流程

### 1. 建立连接
```javascript
const ws = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue');
// 注意：浏览器WebSocket不支持自定义headers，需要服务端代理
```

### 2. 发送StartConnection事件
```javascript
const startConnectionEvent = {
  eventId: 100, // START_CONNECTION
  sessionId: generateSessionId(),
  data: {}
};
```

### 3. 启动会话
```javascript
const startSessionEvent = {
  eventId: 200, // START_SESSION
  sessionId: sessionId,
  data: {
    dialog: {
      bot_name: "豆包",
      dialog_id: "",
      extra: {
        strict_audit: true
      }
    }
  }
};
```

### 4. 发送音频数据
```javascript
const audioEvent = {
  eventId: 400, // AUDIO_DATA
  sessionId: sessionId,
  audioData: pcmAudioBuffer // ArrayBuffer格式
};
```

### 5. 接收服务端事件
- **450**: ASR_INFO - ASR开始识别
- **451**: ASR_RESPONSE - ASR识别结果
- **550**: CHAT_RESPONSE - 大模型回复
- **350**: TTS_SENTENCE_START - TTS开始
- **352**: TTS_RESPONSE - TTS音频数据
- **359**: TTS_ENDED - TTS结束

## 🛠️ 实现要点

### 音频录制
```javascript
// 配置音频录制参数
const audioContext = new AudioContext({
  sampleRate: 16000
});

// 获取麦克风权限
const stream = await navigator.mediaDevices.getUserMedia({
  audio: {
    sampleRate: 16000,
    channelCount: 1,
    echoCancellation: true,
    noiseSuppression: true
  }
});

// 处理音频数据
processor.onaudioprocess = (event) => {
  const inputData = event.inputBuffer.getChannelData(0);
  const pcmData = floatTo16BitPCM(inputData);
  sendAudioData(pcmData.buffer);
};
```

### 音频播放
```javascript
// 播放PCM音频
async function playPCMAudio(audioData) {
  const float32Data = new Float32Array(audioData);
  const audioBuffer = audioContext.createBuffer(1, float32Data.length, 24000);
  audioBuffer.copyToChannel(float32Data, 0);
  
  const source = audioContext.createBufferSource();
  source.buffer = audioBuffer;
  source.connect(audioContext.destination);
  source.start();
}

// 播放OGG/Opus音频
async function playOggAudio(audioData) {
  const audioBuffer = await audioContext.decodeAudioData(audioData);
  const source = audioContext.createBufferSource();
  source.buffer = audioBuffer;
  source.connect(audioContext.destination);
  source.start();
}
```

### 二进制协议
```javascript
// 协议头结构（24字节）
const header = new ArrayBuffer(24);
const headerView = new DataView(header);

headerView.setUint32(0, eventId, true);      // 事件ID（小端序）
headerView.setUint32(4, sessionId, true);    // 会话ID（小端序）
headerView.setUint32(8, dataLength, true);   // 数据长度（小端序）
headerView.setUint32(12, audioLength, true); // 音频长度（小端序）
// 8字节保留字段

// 完整消息 = 协议头 + JSON数据 + 音频数据
```

## ⚠️ 注意事项

### 浏览器限制
1. **WebSocket Headers**: 浏览器WebSocket API不支持自定义headers
2. **解决方案**: 需要通过服务端代理处理认证
3. **CORS**: 需要正确配置跨域请求

### 音频处理
1. **格式转换**: 确保音频格式符合API要求
2. **采样率**: 严格按照文档要求设置
3. **字节序**: 使用小端序编码

### 实时性优化
1. **VAD检测**: 实现语音活动检测，避免发送静音
2. **缓冲管理**: 合理控制音频缓冲区大小
3. **错误处理**: 实现重连和错误恢复机制

## 🔐 安全建议

1. **API密钥**: 不要在前端暴露API密钥
2. **服务端代理**: 通过后端服务处理API调用
3. **环境变量**: 使用环境变量管理敏感配置
4. **HTTPS**: 生产环境必须使用HTTPS

## 📝 当前实现状态

### ✅ 已实现
- 完整的二进制协议处理
- 音频录制和播放
- 实时对话模式
- VAD语音检测
- 后端API密钥管理

### 🚧 演示模式
- 当前运行在演示模式下
- 模拟API响应和音频数据
- 完整的UI交互流程

### 🎯 生产部署
要在生产环境中使用真实API，需要：
1. 实现服务端WebSocket代理
2. 处理真实的二进制协议
3. 集成真实的豆包API端点
4. 完善错误处理和重连机制
